<?php
/**
 * Bamboo User Dashboard - Task Submission Page (Flagship Page)
 * Company: Notepadsly
 * Version: 1.0
 * Description: Core matchmaking/task submission system
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user financial data
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get today's task progress
$tasks_completed_today = getTasksCompletedToday($user_id);
$daily_task_limit = $user_vip['max_daily_tasks'] ?? 5;

// Calculate today's profit
$today_profit_sql = "SELECT SUM(commission_earned) as today_profit 
                     FROM tasks 
                     WHERE user_id = ? AND DATE(completed_at) = CURDATE() AND status = 'completed'";
$today_profit_result = fetchRow($today_profit_sql, [$user_id]);
$today_profit = $today_profit_result['today_profit'] ?? 0;

// Get minimum balance requirement
$min_balance_required = getAppSetting('min_wallet_balance_for_orders', 100);

// Check for active task
$active_task_sql = "SELECT t.*, p.name as product_name, p.image_url, p.price 
                    FROM tasks t 
                    LEFT JOIN products p ON t.product_id = p.id 
                    WHERE t.user_id = ? AND t.status IN ('assigned', 'in_progress') 
                    ORDER BY t.assigned_at DESC LIMIT 1";
$active_task = fetchRow($active_task_sql, [$user_id]);

// Get available products for display (6 products)
$products_sql = "SELECT * FROM products
                 WHERE status = 'active' AND min_vip_level <= ?
                 ORDER BY RAND() LIMIT 6";
$available_products = fetchAll($products_sql, [$user_vip['level'] ?? 1]);

// Page configuration
$page_title = 'Task Submission';
$page_description = 'Product optimization and task submission system';
$page_css = 'tasks.css';
$page_js = 'tasks.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Task Header -->
    <div class="task-header user-fade-in">
        <div class="task-header-content">
            <h1 class="task-title">Starting</h1>
            <div class="user-greeting">
                <span class="username"><?php echo htmlspecialchars($current_user['username']); ?></span>
            </div>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="task-stats-container user-slide-in">
        <div class="user-row">
            <div class="user-col-6">
                <div class="stat-card profit-card">
                    <div class="stat-label">Today Profit</div>
                    <div class="stat-value" id="todayProfit">USDT <?php echo formatCurrency($today_profit); ?></div>
                    <div class="stat-note">Daily Earnings are automatically Updated</div>
                </div>
            </div>
            <div class="user-col-6">
                <div class="stat-card balance-card">
                    <div class="stat-label">Today Balance</div>
                    <div class="stat-value" id="todayBalance">USDT <?php echo formatCurrency($user_balance['balance'] ?? 0); ?></div>
                    <div class="stat-note">Each profit earned will be shown and added to the total</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Progress -->
    <div class="task-progress-container user-fade-in">
        <div class="progress-info">
            <span class="progress-label">Task Progress:</span>
            <span class="progress-count" id="taskProgress"><?php echo $tasks_completed_today; ?>/<?php echo $daily_task_limit; ?></span>
        </div>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: <?php echo ($tasks_completed_today / $daily_task_limit) * 100; ?>%"></div>
        </div>
    </div>

    <!-- Product Grid or Active Task -->
    <div class="task-content">
        <?php if ($active_task): ?>
            <!-- Active Task Display -->
            <div id="activeTaskContainer" class="active-task-container user-fade-in">
                <div class="active-task-card">
                    <div class="task-product-image">
                        <?php if ($active_task['image_url']): ?>
                            <img src="<?php echo htmlspecialchars($active_task['image_url']); ?>" alt="<?php echo htmlspecialchars($active_task['product_name']); ?>">
                        <?php else: ?>
                            <div class="product-placeholder">
                                <i class="icon-product"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="task-details">
                        <h3 class="product-name"><?php echo htmlspecialchars($active_task['product_name']); ?></h3>
                        
                        <div class="task-info-grid">
                            <div class="info-item">
                                <span class="info-label">Price:</span>
                                <span class="info-value">USDT <?php echo formatCurrency($active_task['amount']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Total Amount:</span>
                                <span class="info-value">USDT <?php echo formatCurrency($active_task['amount']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Total Profit:</span>
                                <span class="info-value profit-highlight">USDT <?php echo formatCurrency($active_task['commission_earned'] ?? 0); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Appraisals No:</span>
                                <span class="info-value"><?php echo htmlspecialchars($active_task['appraisal_no'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Creation Time:</span>
                                <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($active_task['assigned_at'])); ?></span>
                            </div>
                        </div>
                        
                        <div class="task-actions">
                            <button id="submitTaskBtn" class="user-btn user-btn-primary user-btn-lg" 
                                    data-task-id="<?php echo $active_task['id']; ?>">
                                <i class="icon-submit"></i>
                                Submit
                            </button>
                            <button id="cancelTaskBtn" class="user-btn user-btn-secondary user-btn-lg" 
                                    data-task-id="<?php echo $active_task['id']; ?>">
                                <i class="icon-close"></i>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Product Grid -->
            <div id="productGrid" class="product-grid user-fade-in">
                <h3 class="grid-title">Available Products</h3>
                <div class="products-container">
                    <?php foreach ($available_products as $index => $product): ?>
                        <div class="product-item" data-product-id="<?php echo $product['id']; ?>">
                            <div class="product-image">
                                <?php if ($product['image_url']): ?>
                                    <img src="<?php echo htmlspecialchars($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div class="product-placeholder">
                                        <i class="icon-product"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="product-overlay">
                                <span class="product-name"><?php echo htmlspecialchars($product['name']); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Start Matching Button -->
            <div class="matching-controls user-fade-in">
                <button id="startMatchingBtn" class="user-btn user-btn-primary user-btn-lg start-matching-btn"
                        <?php if ($user_balance['balance'] < $min_balance_required): ?>disabled title="Insufficient balance"<?php endif; ?>
                        <?php if ($tasks_completed_today >= $daily_task_limit): ?>disabled title="Daily task limit reached"<?php endif; ?>>
                    <i class="icon-play"></i>
                    Start Matching
                </button>
                
                <?php if ($user_balance['balance'] < $min_balance_required): ?>
                    <div class="warning-message">
                        <i class="icon-warning"></i>
                        Insufficient balance. Minimum required: USDT <?php echo formatCurrency($min_balance_required); ?>
                        <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-sm user-btn-outline">Deposit Now</a>
                    </div>
                <?php endif; ?>
                
                <?php if ($tasks_completed_today >= $daily_task_limit): ?>
                    <div class="info-message">
                        <i class="icon-info"></i>
                        Daily task limit reached. Come back tomorrow or upgrade your VIP level.
                        <a href="<?php echo BASE_URL; ?>user/vip/" class="user-btn user-btn-sm user-btn-outline">View VIP Levels</a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Task Instructions -->
    <div class="task-instructions user-fade-in">
        <div class="user-card">
            <div class="user-card-header">
                <h5 class="user-card-title">How It Works</h5>
            </div>
            <div class="user-card-body">
                <ol class="instruction-list">
                    <li>Click "Start Matching" to begin product optimization</li>
                    <li>System will randomly assign a product based on your VIP level</li>
                    <li>Product amount will be temporarily deducted from your balance</li>
                    <li>Review the product details and click "Submit" to complete the task</li>
                    <li>Your balance and profit will be automatically updated upon submission</li>
                    <li>Complete all daily tasks to maximize your earnings</li>
                </ol>
                
                <div class="important-note">
                    <strong>Important:</strong> You must submit the task to get your money and profit back. 
                    Closing without submission will keep your balance deducted.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script>
    window.TaskData = {
        userId: <?php echo $user_id; ?>,
        currentBalance: <?php echo $user_balance['balance'] ?? 0; ?>,
        todayProfit: <?php echo $today_profit; ?>,
        tasksCompleted: <?php echo $tasks_completed_today; ?>,
        dailyLimit: <?php echo $daily_task_limit; ?>,
        minBalance: <?php echo $min_balance_required; ?>,
        hasActiveTask: <?php echo $active_task ? 'true' : 'false'; ?>,
        activeTaskId: <?php echo $active_task['id'] ?? 'null'; ?>
    };
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
