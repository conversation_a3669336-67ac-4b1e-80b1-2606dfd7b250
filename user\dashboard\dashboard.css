/**
 * Bamboo User Dashboard - Dashboard Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: Specific styles for the main dashboard page
 */

/* ===== DASHBOARD WELCOME SECTION ===== */
.dashboard-welcome {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    box-shadow: var(--user-shadow);
}

.welcome-content h1 {
    margin: 0 0 var(--user-spacing-xs) 0;
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
}

.welcome-content p {
    margin: 0;
    opacity: 0.9;
    font-size: var(--user-font-size-lg);
}

.welcome-actions {
    display: flex;
    gap: var(--user-spacing);
}

.welcome-actions .user-btn {
    min-width: 120px;
}

.welcome-actions .user-btn-outline {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.welcome-actions .user-btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* ===== BALANCE CARDS ===== */
.balance-card {
    border: none;
    background: var(--user-card-bg);
    transition: var(--user-transition);
    height: 100%;
}

.balance-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow-lg);
}

.balance-card .user-card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg);
}

.balance-info {
    display: flex;
    align-items: center;
    gap: var(--user-spacing);
}

.balance-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
}

.balance-icon.commission {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.balance-icon.total {
    background: linear-gradient(135deg, var(--user-info), #17a2b8);
}

.balance-icon.vip {
    background: linear-gradient(135deg, var(--user-warning), #ffc107);
}

.balance-details h3 {
    margin: 0;
    font-size: var(--user-font-size-xl);
    font-weight: 700;
    color: var(--user-text-primary);
}

.balance-details p {
    margin: 0;
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.balance-actions {
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-xs);
}

/* ===== TASK OVERVIEW ===== */
.task-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--user-spacing);
    margin-bottom: var(--user-spacing-lg);
    padding: var(--user-spacing-lg);
    background-color: var(--user-border-light);
    border-radius: var(--user-border-radius);
}

.task-stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--user-font-size-xl);
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.stat-label {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.active-tasks h6 {
    margin: 0 0 var(--user-spacing) 0;
    font-weight: 600;
    color: var(--user-text-primary);
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing);
    border: var(--user-border-width) solid var(--user-border-color);
    border-radius: var(--user-border-radius);
    margin-bottom: var(--user-spacing-sm);
    transition: var(--user-transition);
}

.task-item:hover {
    background-color: var(--user-border-light);
}

.task-info {
    flex: 1;
}

.task-title {
    font-weight: 500;
    color: var(--user-text-primary);
    margin-bottom: 2px;
}

.task-amount {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.status-badge {
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
}

.status-assigned {
    background-color: var(--user-info);
    color: white;
}

.status-in_progress {
    background-color: var(--user-warning);
    color: var(--user-text-primary);
}

.status-completed {
    background-color: var(--user-success);
    color: white;
}

.no-active-tasks {
    text-align: center;
    padding: var(--user-spacing-xl);
    color: var(--user-text-muted);
}

.no-active-tasks a {
    color: var(--user-primary);
    text-decoration: none;
}

/* ===== TRANSACTION LIST ===== */
.transaction-list {
    max-height: 300px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--user-spacing);
    padding: var(--user-spacing);
    border-bottom: var(--user-border-width) solid var(--user-border-light);
    transition: var(--user-transition);
}

.transaction-item:hover {
    background-color: var(--user-border-light);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--user-border-light);
    color: var(--user-text-secondary);
}

.transaction-details {
    flex: 1;
}

.transaction-type {
    font-weight: 500;
    color: var(--user-text-primary);
    margin-bottom: 2px;
}

.transaction-date {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.transaction-amount {
    font-weight: 600;
    font-size: var(--user-font-size-lg);
}

.transaction-amount.positive {
    color: var(--user-success);
}

.transaction-amount.negative {
    color: var(--user-danger);
}

.no-transactions {
    text-align: center;
    padding: var(--user-spacing-xl);
    color: var(--user-text-muted);
}

/* ===== VIP PROGRESS ===== */
.vip-current {
    text-align: center;
    margin-bottom: var(--user-spacing-lg);
}

.vip-level {
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.vip-name {
    font-size: var(--user-font-size-lg);
    color: var(--user-text-secondary);
}

.vip-progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--user-border-light);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--user-spacing-sm);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    transition: width 0.5s ease;
}

.vip-progress-text {
    text-align: center;
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
    margin-bottom: var(--user-spacing);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--user-spacing);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--user-spacing-xs);
    padding: var(--user-spacing-lg);
    text-decoration: none;
    color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    box-shadow: var(--user-shadow-sm);
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
    background: linear-gradient(135deg, var(--user-gradient-end), var(--user-primary));
}

.quick-action-btn:nth-child(1) {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.quick-action-btn:nth-child(2) {
    background: linear-gradient(135deg, var(--user-warning), #ffc107);
}

.quick-action-btn:nth-child(3) {
    background: linear-gradient(135deg, var(--user-info), #17a2b8);
}

.quick-action-btn:nth-child(4) {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
}

.quick-action-btn i {
    font-size: 1.5rem;
}

.quick-action-btn span {
    font-size: var(--user-font-size-sm);
    font-weight: 500;
}

/* ===== NOTIFICATIONS ===== */
.notification-list {
    max-height: 250px;
    overflow-y: auto;
}

.notification-item {
    padding: var(--user-spacing);
    border-bottom: var(--user-border-width) solid var(--user-border-light);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-title {
    font-weight: 500;
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing-xs);
}

.notification-message {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-secondary);
    margin-bottom: var(--user-spacing-xs);
    line-height: 1.4;
}

.notification-time {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 1200px) {
    .dashboard-welcome {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-lg);
    }
    
    .task-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .balance-card .user-card-body {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing);
    }
    
    .task-stats {
        grid-template-columns: 1fr;
    }
    
    .transaction-item {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-sm);
    }
}
