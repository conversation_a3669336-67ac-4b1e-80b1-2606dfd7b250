/**
 * Bamboo User Dashboard - Dashboard Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: Specific styles for the main dashboard page
 */

/* ===== DASHBOARD WELCOME SECTION ===== */
.dashboard-welcome {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-xl);
    border-radius: var(--user-border-radius-lg);
    margin-bottom: var(--user-spacing-xl);
    box-shadow: var(--user-shadow);
}

.welcome-content h1 {
    margin: 0 0 var(--user-spacing-xs) 0;
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
}

.welcome-content p {
    margin: 0;
    opacity: 0.9;
    font-size: var(--user-font-size-lg);
}

.welcome-actions {
    display: flex;
    gap: var(--user-spacing);
}

.welcome-actions .user-btn {
    min-width: 120px;
}

.welcome-actions .user-btn-outline {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.welcome-actions .user-btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* ===== BALANCE CARDS ===== */
.balance-card {
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    transition: var(--user-transition);
    height: 100%;
    margin-bottom: var(--user-spacing-lg);
}

.balance-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.95));
}

.balance-card .user-card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg);
}

.balance-info {
    display: flex;
    align-items: center;
    gap: var(--user-spacing);
}

.balance-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
}

.balance-icon.commission {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.balance-icon.total {
    background: linear-gradient(135deg, var(--user-info), #17a2b8);
}

.balance-icon.vip {
    background: linear-gradient(135deg, var(--user-warning), #ffc107);
}

.balance-details h3 {
    margin: 0;
    font-size: var(--user-font-size-xl);
    font-weight: 700;
    color: var(--user-text-primary);
}

.balance-details p {
    margin: 0;
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.balance-actions {
    display: flex;
    flex-direction: column;
    gap: var(--user-spacing-xs);
}

/* ===== TASK OVERVIEW ===== */
.task-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--user-spacing);
    margin-bottom: var(--user-spacing-lg);
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border-radius: var(--user-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.task-stats .task-stat-item {
    padding: var(--user-spacing);
    border-right: 1px solid rgba(0, 0, 0, 0.08);
}

.task-stats .task-stat-item:last-child {
    border-right: none;
}

.task-stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--user-font-size-xl);
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.stat-label {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.active-tasks h6 {
    margin: 0 0 var(--user-spacing) 0;
    font-weight: 600;
    color: var(--user-text-primary);
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius);
    margin-bottom: var(--user-spacing);
    transition: var(--user-transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.task-item:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.task-item + .task-item {
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    margin-top: 0;
}

.task-info {
    flex: 1;
}

.task-title {
    font-weight: 500;
    color: var(--user-text-primary);
    margin-bottom: 2px;
}

.task-amount {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

.status-badge {
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
}

.status-assigned {
    background-color: var(--user-info);
    color: white;
}

.status-in_progress {
    background-color: var(--user-warning);
    color: var(--user-text-primary);
}

.status-completed {
    background-color: var(--user-success);
    color: white;
}

.no-active-tasks {
    text-align: center;
    padding: var(--user-spacing-xl);
    color: var(--user-text-muted);
}

.no-active-tasks a {
    color: var(--user-primary);
    text-decoration: none;
}

/* ===== TRANSACTION TABLE ===== */
.transaction-table-container {
    background: white;
    border-radius: var(--user-border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.transaction-table {
    display: flex;
    flex-direction: column;
}

.transaction-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    transition: var(--user-transition);
    border-bottom: 1px solid #f1f5f9;
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-row.row-white {
    background: white;
}

.transaction-row.row-light {
    background: #f8fafc;
}

.transaction-row:hover {
    background: #e0f2fe !important;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.transaction-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.transaction-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.transaction-details {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: var(--user-text-primary);
    margin-bottom: 2px;
    font-size: 0.95rem;
}

.transaction-date {
    font-size: 0.8rem;
    color: var(--user-text-muted);
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
    margin-right: 1rem;
    min-width: 100px;
    text-align: right;
}

.transaction-amount.positive {
    color: #10b981;
}

.transaction-amount.negative {
    color: #ef4444;
}

.transaction-status {
    min-width: 80px;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-failed {
    background: #fee2e2;
    color: #991b1b;
}

.status-processing {
    background: #dbeafe;
    color: #1e40af;
}

/* No Transactions State */
.no-transactions {
    text-align: center;
    padding: 2rem;
    color: var(--user-text-muted);
}

.no-transactions-icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.no-transactions p {
    margin-bottom: 1rem;
    font-size: 1rem;
}

/* ===== VIP PROGRESS ===== */
.vip-current {
    text-align: center;
    margin-bottom: var(--user-spacing-lg);
}

.vip-level {
    font-size: var(--user-font-size-xxl);
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.vip-name {
    font-size: var(--user-font-size-lg);
    color: var(--user-text-secondary);
}

.vip-progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--user-border-light);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--user-spacing-sm);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    transition: width 0.5s ease;
}

.vip-progress-text {
    text-align: center;
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
    margin-bottom: var(--user-spacing);
}

/* ===== QUICK MENU ===== */
.user-card-subtitle {
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
    margin: var(--user-spacing-xs) 0 0 0;
}

/* Quick Menu Sections */
.quick-menu-section {
    margin-bottom: 2rem;
}

.quick-menu-section:last-child {
    margin-bottom: 0;
}

.menu-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--user-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.quick-menu-grid {
    display: grid;
    gap: 1rem;
}

.primary-actions {
    grid-template-columns: repeat(4, 1fr);
}

.secondary-actions {
    grid-template-columns: repeat(2, 1fr);
}

.quick-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.25rem;
    text-decoration: none;
    background: white;
    border: 2px solid #f1f5f9;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.quick-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-menu-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--user-primary);
    text-decoration: none;
}

.quick-menu-item:hover::before {
    transform: scaleX(1);
}

.primary-item .menu-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.secondary-item .menu-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Icon Colors */
.deposit-icon { background: linear-gradient(135deg, #10b981, #059669); }
.withdraw-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
.team-icon { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.certificate-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.faq-icon { background: linear-gradient(135deg, #ef4444, #dc2626); }
.about-icon { background: linear-gradient(135deg, #06b6d4, #0891b2); }

.menu-content h6 {
    margin: 0 0 0.25rem 0;
    color: var(--user-text-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.menu-content p {
    margin: 0;
    color: var(--user-text-muted);
    font-size: 0.8rem;
    line-height: 1.3;
}

/* ===== NOTIFICATIONS ===== */
.notification-list {
    max-height: 250px;
    overflow-y: auto;
}

.notification-item {
    padding: var(--user-spacing);
    border-bottom: var(--user-border-width) solid var(--user-border-light);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-title {
    font-weight: 500;
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing-xs);
}

.notification-message {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-secondary);
    margin-bottom: var(--user-spacing-xs);
    line-height: 1.4;
}

.notification-time {
    font-size: var(--user-font-size-sm);
    color: var(--user-text-muted);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 1200px) {
    .dashboard-welcome {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-lg);
    }
    
    .task-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .primary-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .secondary-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .balance-card .user-card-body {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing);
    }
    
    .task-stats {
        grid-template-columns: 1fr;
    }
    
    .transaction-item {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-sm);
    }

    .primary-actions,
    .secondary-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quick-menu-item {
        padding: 1rem;
    }

    .primary-item .menu-icon,
    .secondary-item .menu-icon {
        width: 50px;
        height: 50px;
    }
}

/* ===== USDT MODAL STYLES ===== */
.usdt-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.usdt-modal-dialog {
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.usdt-modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    background: white;
    animation: slideIn 0.3s ease-out;
}

.usdt-modal-header {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 1.5rem;
    position: relative;
    text-align: center;
}

.usdt-modal-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    animation: bounce 2s infinite;
}

.usdt-modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.usdt-modal-subtitle {
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
    opacity: 0.9;
}

.usdt-modal-close {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.usdt-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.usdt-modal-body {
    padding: 1.5rem;
    background: white;
    text-align: center;
}

.usdt-highlight-box {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #f59e0b;
}

.usdt-multiplier {
    font-size: 2rem;
    font-weight: 900;
    color: #d97706;
    margin-bottom: 0.25rem;
}

.usdt-label {
    font-size: 0.875rem;
    color: #92400e;
    font-weight: 600;
}

.usdt-message {
    font-size: 0.9rem;
    color: #374151;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.usdt-features {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #10b981;
    font-weight: 600;
    font-size: 0.8rem;
}

.feature-item i {
    font-size: 1rem;
}

.usdt-action-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    transition: all 0.3s ease;
    width: 100%;
}

.usdt-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

/* Modal positioning */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

/* Modal animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@media (max-width: 768px) {
    .usdt-modal-dialog {
        width: 95%;
        max-width: 320px;
    }

    .usdt-modal-header {
        padding: 1.25rem;
    }

    .usdt-modal-body {
        padding: 1.25rem;
    }

    .usdt-features {
        flex-direction: column;
        gap: 0.5rem;
    }
}
