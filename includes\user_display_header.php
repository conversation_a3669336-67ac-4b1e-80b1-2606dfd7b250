<?php
// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Ensure $current_user and $user_vip are available
if (!isset($current_user) || !isset($user_vip)) {
    // This file expects these variables to be set by the including page.
    // In a production environment, you might fetch them here if not set,
    // but for this task, we assume the parent page provides them.
    // Fallback in case of direct access or missing variables (though parent pages should define them)
    // You might want to add more robust error handling or data fetching here.
    if (function_exists('getCurrentUser')) {
        $current_user = getCurrentUser();
    } else {
        $current_user = ['username' => 'Guest'];
    }
    if (function_exists('getUserVipLevel') && isset($current_user['id'])) {
        $user_vip = getUserVipLevel($current_user['id']);
    } else {
        $user_vip = ['level' => 1, 'name' => 'Bronze'];
    }
}

$username = htmlspecialchars($current_user['username'] ?? 'Guest');
$vip_level = $user_vip['level'] ?? 1;
$vip_name = htmlspecialchars($user_vip['name'] ?? 'Bronze');
?>

<div class="user-display-header">
    <div class="user-info-section">
        <span class="user-info-username"><?php echo $username; ?></span>
        <span class="user-info-vip-badge">VIP <?php echo $vip_level; ?> - <?php echo $vip_name; ?></span>
    </div>
    <!-- Potentially add more elements like avatar or balance here if needed consistently -->
</div>