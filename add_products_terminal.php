<?php
/**
 * Terminal Script to Add 20 Diverse Products
 * Run from Bamboo root directory: php add_products_terminal.php
 */

// Include required files with correct paths
require_once 'includes/config.php';
require_once 'includes/db.php';

echo "=== BAMBOO PRODUCT DATABASE POPULATION ===\n";
echo "Adding 20 diverse products for all VIP levels...\n\n";

try {
    // Check current product count
    $count_sql = "SELECT COUNT(*) as count FROM products WHERE status = 'active'";
    $count_result = $conn->query($count_sql);
    $current_count = $count_result->fetch_assoc()['count'];
    
    echo "Current active products: $current_count\n\n";
    
    // Define 20 diverse products across all VIP levels and categories
    $products = [
        // VIP 1 Products (Entry Level - $50-$300)
        ['name' => 'Wireless Bluetooth Earbuds', 'price' => 89.99, 'category_id' => 1, 'min_vip_level' => 1, 'commission_rate' => 12.00],
        ['name' => 'Cotton T-Shirt Set', 'price' => 45.00, 'category_id' => 2, 'min_vip_level' => 1, 'commission_rate' => 15.00],
        ['name' => 'LED Desk Lamp', 'price' => 65.00, 'category_id' => 3, 'min_vip_level' => 1, 'commission_rate' => 18.00],
        ['name' => 'Yoga Mat Premium', 'price' => 55.00, 'category_id' => 4, 'min_vip_level' => 1, 'commission_rate' => 20.00],
        ['name' => 'Skincare Starter Kit', 'price' => 79.99, 'category_id' => 5, 'min_vip_level' => 1, 'commission_rate' => 16.00],
        
        // VIP 2 Products (Intermediate - $300-$800)
        ['name' => 'Smart Watch Series 8', 'price' => 399.00, 'category_id' => 1, 'min_vip_level' => 2, 'commission_rate' => 10.00],
        ['name' => 'Designer Leather Jacket', 'price' => 599.00, 'category_id' => 2, 'min_vip_level' => 2, 'commission_rate' => 8.00],
        ['name' => 'Robot Vacuum Cleaner', 'price' => 449.00, 'category_id' => 3, 'min_vip_level' => 2, 'commission_rate' => 12.00],
        ['name' => 'Professional Dumbbells Set', 'price' => 299.00, 'category_id' => 4, 'min_vip_level' => 2, 'commission_rate' => 15.00],
        ['name' => 'Professional Hair Dryer', 'price' => 349.00, 'category_id' => 5, 'min_vip_level' => 2, 'commission_rate' => 13.00],
        
        // VIP 3 Products (Advanced - $800-$2000)
        ['name' => 'Gaming Laptop RTX 4060', 'price' => 1299.00, 'category_id' => 1, 'min_vip_level' => 3, 'commission_rate' => 7.00],
        ['name' => 'Luxury Designer Handbag', 'price' => 1599.00, 'category_id' => 2, 'min_vip_level' => 3, 'commission_rate' => 6.00],
        ['name' => 'Smart Home Security System', 'price' => 899.00, 'category_id' => 3, 'min_vip_level' => 3, 'commission_rate' => 9.00],
        ['name' => 'Professional Treadmill', 'price' => 1799.00, 'category_id' => 4, 'min_vip_level' => 3, 'commission_rate' => 8.00],
        ['name' => 'Premium Skincare Collection', 'price' => 999.00, 'category_id' => 5, 'min_vip_level' => 3, 'commission_rate' => 10.00],
        
        // VIP 4 Products (Premium - $2000-$5000)
        ['name' => 'MacBook Pro M3 Max', 'price' => 3499.00, 'category_id' => 1, 'min_vip_level' => 4, 'commission_rate' => 5.00],
        ['name' => 'Swiss Luxury Watch', 'price' => 4299.00, 'category_id' => 2, 'min_vip_level' => 4, 'commission_rate' => 4.00],
        ['name' => 'Premium Kitchen Appliance Set', 'price' => 2899.00, 'category_id' => 3, 'min_vip_level' => 4, 'commission_rate' => 6.00],
        ['name' => 'Commercial Gym Equipment', 'price' => 3999.00, 'category_id' => 4, 'min_vip_level' => 4, 'commission_rate' => 5.50],
        
        // VIP 5 Products (Elite - $5000+)
        ['name' => 'Professional Workstation PC', 'price' => 7999.00, 'category_id' => 1, 'min_vip_level' => 5, 'commission_rate' => 3.50],
        ['name' => 'Professional Spa Equipment', 'price' => 15999.00, 'category_id' => 5, 'min_vip_level' => 5, 'commission_rate' => 2.50]
    ];
    
    $added_count = 0;
    $skipped_count = 0;
    $errors = [];
    
    echo "Processing " . count($products) . " products...\n";
    echo str_repeat("-", 60) . "\n";
    
    foreach ($products as $index => $product) {
        try {
            // Check if product already exists
            $check_sql = "SELECT id FROM products WHERE name = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $product['name']);
            $check_stmt->execute();
            $exists = $check_stmt->get_result()->num_rows > 0;
            
            if (!$exists) {
                $insert_sql = "INSERT INTO products (name, price, commission_rate, category_id, min_vip_level, max_daily_assignments, weight, stock, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param("sddiiiii", 
                    $product['name'],
                    $product['price'],
                    $product['commission_rate'],
                    $product['category_id'],
                    $product['min_vip_level'],
                    100, // max_daily_assignments
                    1,   // weight
                    100  // stock
                );
                
                if ($insert_stmt->execute()) {
                    $added_count++;
                    printf("✅ [%2d/21] Added: %-30s (VIP %d+) - $%8.2f (%2.1f%%)\n", 
                        $index + 1, 
                        $product['name'], 
                        $product['min_vip_level'], 
                        $product['price'], 
                        $product['commission_rate']
                    );
                } else {
                    $errors[] = "Failed to add: {$product['name']}";
                    echo "❌ Failed to add: {$product['name']}\n";
                }
            } else {
                $skipped_count++;
                printf("⏭️  [%2d/21] Skipped: %-30s (already exists)\n", 
                    $index + 1, 
                    $product['name']
                );
            }
        } catch (Exception $e) {
            $errors[] = "Error adding {$product['name']}: " . $e->getMessage();
            echo "❌ Error adding {$product['name']}: " . $e->getMessage() . "\n";
        }
    }
    
    // Final count
    $final_count_result = $conn->query($count_sql);
    $final_count = $final_count_result->fetch_assoc()['count'];
    
    echo str_repeat("-", 60) . "\n";
    echo "=== SUMMARY ===\n";
    echo "Products added: $added_count\n";
    echo "Products skipped: $skipped_count\n";
    echo "Total processed: " . count($products) . "\n";
    echo "Total active products in database: $final_count\n";
    
    if (!empty($errors)) {
        echo "\n=== ERRORS ===\n";
        foreach ($errors as $error) {
            echo "❌ $error\n";
        }
    }
    
    if ($added_count > 0) {
        echo "\n🎉 SUCCESS: Product database successfully populated with $added_count new products!\n";
        echo "You can now view the products at: http://localhost/Bamboo/admin/products/\n";
        echo "User tasks page: http://localhost/Bamboo/user/tasks/tasks.php\n";
    } else {
        echo "\n✅ All products already exist in the database.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== SCRIPT COMPLETED ===\n";
?>
