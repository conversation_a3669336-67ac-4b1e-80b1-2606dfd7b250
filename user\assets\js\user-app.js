/**
 * Bamboo User Dashboard - Main Application JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Core functionality for user dashboard
 */

// Global UserApp object
window.UserApp = window.UserApp || {};

// Main application object
UserApp = {
    // Configuration (will be set by PHP)
    config: {
        baseUrl: '',
        assetsUrl: '',
        csrfToken: '',
        userId: null,
        username: '',
        vipLevel: 1,
        balance: 0,
        commissionBalance: 0,
        ajaxTimeout: 30000,
        loadingDelay: 300,
        animationDuration: 300
    },

    // Initialize application
    init: function() {
        this.setupAjax();
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupNotifications();
        this.setupLoadingStates();
        this.setupCSRFToken();
        this.initializeComponents();
        this.hideLoadingSpinner();
        console.log('UserApp initialized successfully');
    },

    // Setup AJAX defaults
    setupAjax: function() {
        $.ajaxSetup({
            timeout: this.config.ajaxTimeout,
            headers: {
                'X-CSRF-TOKEN': this.config.csrfToken
            },
            beforeSend: function(xhr, settings) {
                // Add CSRF token to all requests
                if (settings.type !== 'GET') {
                    if (settings.data && typeof settings.data === 'string') {
                        settings.data += '&csrf_token=' + UserApp.config.csrfToken;
                    } else if (settings.data && typeof settings.data === 'object') {
                        settings.data.csrf_token = UserApp.config.csrfToken;
                    }
                }
            },
            error: function(xhr, status, error) {
                if (status === 'timeout') {
                    UserApp.showNotification('Request timed out. Please try again.', 'error');
                } else if (xhr.status === 401) {
                    UserApp.showNotification('Session expired. Please login again.', 'error');
                    setTimeout(() => {
                        window.location.href = UserApp.config.baseUrl + 'user/login/';
                    }, 2000);
                } else if (xhr.status === 403) {
                    UserApp.showNotification('Access denied.', 'error');
                } else if (xhr.status >= 500) {
                    UserApp.showNotification('Server error. Please try again later.', 'error');
                }
            }
        });
    },

    // Setup event listeners
    setupEventListeners: function() {
        // Handle AJAX forms
        $(document).on('submit', 'form[data-ajax="true"]', this.handleAjaxForm);
        
        // Handle confirmation dialogs
        $(document).on('click', '[data-confirm]', this.handleConfirmation);
        
        // Handle loading buttons
        $(document).on('click', '[data-loading-text]', this.handleLoadingButton);
        
        // Handle copy to clipboard
        $(document).on('click', '[data-copy]', this.handleCopyToClipboard);
        
        // Handle refresh buttons
        $(document).on('click', '[data-refresh]', this.handleRefresh);
        
        // Handle navigation
        $(document).on('click', '[data-navigate]', this.handleNavigation);
        
        // Handle balance refresh
        $(document).on('click', '.refresh-balance', this.refreshBalance);
        
        // Handle logout
        $(document).on('click', '.user-logout', this.handleLogout);
    },

    // Setup form validation
    setupFormValidation: function() {
        // Real-time validation for forms
        $(document).on('input', '.user-form-control[required]', function() {
            const $field = $(this);
            const value = $field.val().trim();
            
            if (value === '') {
                $field.addClass('is-invalid').removeClass('is-valid');
            } else {
                $field.addClass('is-valid').removeClass('is-invalid');
            }
        });

        // Email validation
        $(document).on('input', 'input[type="email"]', function() {
            const $field = $(this);
            const email = $field.val().trim();
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (email && !emailRegex.test(email)) {
                $field.addClass('is-invalid').removeClass('is-valid');
            } else if (email) {
                $field.addClass('is-valid').removeClass('is-invalid');
            }
        });

        // Phone validation
        $(document).on('input', 'input[type="tel"]', function() {
            const $field = $(this);
            const phone = $field.val().trim();
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            
            if (phone && !phoneRegex.test(phone)) {
                $field.addClass('is-invalid').removeClass('is-valid');
            } else if (phone) {
                $field.addClass('is-valid').removeClass('is-invalid');
            }
        });
    },

    // Setup notifications system
    setupNotifications: function() {
        // Create notification container if it doesn't exist
        if (!$('#user-notifications').length) {
            $('body').append('<div id="user-notifications" class="user-notifications"></div>');
        }
    },

    // Setup loading states
    setupLoadingStates: function() {
        // Global loading overlay
        if (!$('#user-loading-overlay').length) {
            $('body').append(`
                <div id="user-loading-overlay" class="user-loading-overlay" style="display: none;">
                    <div class="user-loading-spinner">
                        <div class="spinner"></div>
                        <div class="loading-text">Loading...</div>
                    </div>
                </div>
            `);
        }
    },

    // Setup CSRF token refresh
    setupCSRFToken: function() {
        // Refresh CSRF token every 25 minutes
        setInterval(() => {
            this.refreshCSRFToken();
        }, 25 * 60 * 1000);
    },

    // Initialize components
    initializeComponents: function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
        
        // Initialize popovers
        $('[data-toggle="popover"]').popover();
        
        // Initialize dropdowns
        $('.user-dropdown-toggle').on('click', function(e) {
            e.preventDefault();
            $(this).next('.user-dropdown-menu').toggle();
        });

        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.user-dropdown').length) {
                $('.user-dropdown-menu').hide();
            }
        });
    },

    // Handle AJAX form submissions
    handleAjaxForm: function(e) {
        e.preventDefault();
        const $form = $(this);
        const url = $form.attr('action') || window.location.href;
        const method = $form.attr('method') || 'POST';
        const data = $form.serialize();

        UserApp.showLoading();

        $.ajax({
            url: url,
            method: method,
            data: data,
            dataType: 'json',
            success: function(response) {
                UserApp.hideLoading();
                
                if (response.success) {
                    UserApp.showNotification(response.message || 'Operation completed successfully', 'success');
                    
                    // Handle redirects
                    if (response.redirect) {
                        setTimeout(() => {
                            window.location.href = response.redirect;
                        }, 1500);
                    }
                    
                    // Handle form reset
                    if (response.reset_form) {
                        $form[0].reset();
                    }
                    
                    // Handle custom callbacks
                    if (response.callback && typeof window[response.callback] === 'function') {
                        window[response.callback](response);
                    }
                } else {
                    UserApp.showNotification(response.message || 'Operation failed', 'error');
                }
            },
            error: function() {
                UserApp.hideLoading();
                UserApp.showNotification('Network error occurred. Please try again.', 'error');
            }
        });
    },

    // Handle confirmation dialogs
    handleConfirmation: function(e) {
        e.preventDefault();
        const $element = $(this);
        const message = $element.data('confirm') || 'Are you sure?';
        
        if (confirm(message)) {
            // If it's a link, navigate to it
            if ($element.is('a')) {
                window.location.href = $element.attr('href');
            }
            // If it's a form button, submit the form
            else if ($element.is('button') || $element.is('input[type="submit"]')) {
                $element.closest('form').submit();
            }
            // If it has a data-action, execute it
            else if ($element.data('action')) {
                const action = $element.data('action');
                if (typeof UserApp[action] === 'function') {
                    UserApp[action]($element);
                }
            }
        }
    },

    // Handle copy to clipboard
    handleCopyToClipboard: function(e) {
        e.preventDefault();
        const $element = $(this);
        const text = $element.data('copy') || $element.text();
        
        navigator.clipboard.writeText(text).then(() => {
            UserApp.showNotification('Copied to clipboard!', 'success');
        }).catch(() => {
            UserApp.showNotification('Failed to copy to clipboard', 'error');
        });
    },

    // Handle refresh actions
    handleRefresh: function(e) {
        e.preventDefault();
        const $element = $(this);
        const target = $element.data('refresh');
        
        if (target === 'page') {
            window.location.reload();
        } else if (target === 'balance') {
            UserApp.refreshBalance();
        } else if (typeof UserApp['refresh' + target] === 'function') {
            UserApp['refresh' + target]();
        }
    },

    // Handle navigation
    handleNavigation: function(e) {
        e.preventDefault();
        const $element = $(this);
        const url = $element.data('navigate');
        
        if (url) {
            window.location.href = UserApp.config.baseUrl + url;
        }
    },

    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        const $container = $('#user-notifications');
        const id = 'notification-' + Date.now();
        const typeClass = 'user-notification-' + type;
        
        const $notification = $(`
            <div id="${id}" class="user-notification ${typeClass} user-fade-in">
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="UserApp.hideNotification('${id}')">&times;</button>
                </div>
            </div>
        `);
        
        $container.append($notification);
        
        // Auto-hide after duration
        setTimeout(() => {
            this.hideNotification(id);
        }, duration);
    },

    // Hide notification
    hideNotification: function(id) {
        const $notification = $('#' + id);
        $notification.fadeOut(300, function() {
            $(this).remove();
        });
    },

    // Show loading overlay
    showLoading: function(text = 'Loading...') {
        $('#user-loading-overlay .loading-text').text(text);
        $('#user-loading-overlay').fadeIn(200);
    },

    // Hide loading overlay
    hideLoading: function() {
        $('#user-loading-overlay').fadeOut(200);
    },

    // Hide initial loading spinner
    hideLoadingSpinner: function() {
        $('.initial-loading').fadeOut(500);
    },

    // Refresh CSRF token
    refreshCSRFToken: function() {
        $.get(this.config.baseUrl + 'api/csrf-token.php')
            .done((response) => {
                if (response.success) {
                    this.config.csrfToken = response.token;
                }
            });
    },

    // Refresh user balance
    refreshBalance: function() {
        $.get(UserApp.config.baseUrl + 'user/api/balance.php')
            .done((response) => {
                if (response.success) {
                    UserApp.config.balance = response.balance;
                    UserApp.config.commissionBalance = response.commission_balance;
                    
                    // Update balance displays
                    $('.user-balance').text(response.balance_formatted);
                    $('.user-commission-balance').text(response.commission_balance_formatted);
                    
                    UserApp.showNotification('Balance updated', 'success', 2000);
                }
            });
    },

    // Handle logout
    handleLogout: function(e) {
        e.preventDefault();
        
        if (confirm('Are you sure you want to logout?')) {
            window.location.href = UserApp.config.baseUrl + 'user/logout/';
        }
    },

    // Utility function to format currency
    formatCurrency: function(amount, currency = 'USDT') {
        return parseFloat(amount).toFixed(2) + ' ' + currency;
    },

    // Utility function to format numbers
    formatNumber: function(number, decimals = 0) {
        return parseFloat(number).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }
};

// Initialize when DOM is ready
$(document).ready(function() {
    UserApp.init();
});
