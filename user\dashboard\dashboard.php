<?php
/**
 * Bamboo User Dashboard - Main Dashboard Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: Main user dashboard with balance, VIP status, and quick actions
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user financial data
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get dashboard statistics
$dashboard_stats = [
    'total_tasks_completed' => getTotalTasksCompleted($user_id),
    'tasks_completed_today' => getTasksCompletedToday($user_id),
    'total_commission_earned' => getTotalCommissionEarned($user_id),
    'referral_count' => getReferralCount($user_id),
    'pending_withdrawals' => getPendingWithdrawals($user_id),
    'recent_transactions' => getRecentTransactions($user_id, 5),
    'active_tasks' => getActiveTasks($user_id),
    'vip_progress' => getVipProgress($user_id)
];

// Get recent notifications
$recent_notifications = getRecentNotifications($user_id, 3);

// Page configuration
$page_title = 'Dashboard';
$page_description = 'User dashboard with balance overview and quick actions';
$page_css = 'dashboard.css';
$page_js = 'dashboard.js';

// Check if this is the first login today (for popup)
$show_welcome_popup = false;
$last_login = $current_user['last_login'] ?? null;
if (!$last_login || date('Y-m-d', strtotime($last_login)) !== date('Y-m-d')) {
    $show_welcome_popup = true;
    // Update last login
    executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user_id]);
}

// Get app settings for popup
$app_logo = getAppSetting('app_logo', '');
$app_name = getAppSetting('app_name', APP_NAME);
$usdt_multiplier = getAppSetting('usdt_multiplier_x', '10');
$welcome_bonus_message = getAppSetting('welcome_bonus_message', 'During the anniversary celebration, New users can get a bonus for the first time to complete group tasks.');

// Get notification banner message
$notification_banner = fetchRow("SELECT message FROM notifications_banner WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1");
$banner_message = $notification_banner['message'] ?? 'Welcome to Kompyte! 🎉 USDT 10X bonus available for new users completing group tasks. Start earning today!';

// USDT notification for login
$usdt_notification = "🚀 USDT Earning Alert: Complete daily tasks to earn up to " . $usdt_multiplier . "X bonus rewards! Your current VIP level: " . ($user_vip['name'] ?? 'Bronze');

// Include header
include '../includes/user_header.php';
?>

<!-- Welcome Popup -->
<?php if ($show_welcome_popup): ?>
<div id="welcomePopup" class="login-popup-overlay">
    <div class="login-popup">
        <div class="popup-logo">
            <?php if ($app_logo): ?>
                <img src="<?php echo htmlspecialchars($app_logo); ?>" alt="<?php echo htmlspecialchars($app_name); ?>">
            <?php else: ?>
                <?php echo strtoupper(substr($app_name, 0, 1)); ?>
            <?php endif; ?>
        </div>
        <div class="popup-title"><?php echo htmlspecialchars($app_name); ?></div>
        <div class="popup-usdt-bonus">USDT <?php echo htmlspecialchars($usdt_multiplier); ?>X</div>
        <div class="popup-message"><?php echo htmlspecialchars($welcome_bonus_message); ?></div>
        <button class="popup-close-btn" onclick="closeWelcomePopup()">Get Started</button>
    </div>
</div>
<?php endif; ?>

<!-- USDT Notification Banner -->
<div id="usdtNotificationBanner" class="usdt-notification-banner">
    <div class="usdt-notification-content">
        <div class="usdt-icon">💰</div>
        <span class="usdt-text">USDT 9X Subscription during the anniversary celebration. New users can earn a bonus for the first to complete a group.</span>
    </div>
    <button class="usdt-close" onclick="closeUsdtBanner()" title="Close">&times;</button>
</div>

<div class="user-container">
    <!-- Welcome Section -->
    <div class="dashboard-welcome user-fade-in">
        <div class="welcome-content">
            <h1 class="welcome-title">Welcome back, <?php echo htmlspecialchars($current_user['username']); ?>!</h1>
            <p class="welcome-subtitle">Here's your account overview and recent activity</p>
        </div>
        <div class="welcome-actions">
            <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-btn user-btn-primary">
                <i class="icon-tasks"></i>
                Start Tasks
            </a>
            <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-outline">
                <i class="icon-deposit"></i>
                Deposit
            </a>
        </div>
    </div>

    <!-- Balance Overview Cards -->
    <div class="user-row user-mb-4">
        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon">
                            <i class="icon-wallet"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($user_balance['balance'] ?? 0); ?></h3>
                            <p class="balance-label">Main Balance</p>
                        </div>
                    </div>
                    <div class="balance-actions">
                        <button class="refresh-balance user-btn user-btn-sm" title="Refresh">
                            <i class="icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.1s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon commission">
                            <i class="icon-commission"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($user_balance['commission_balance'] ?? 0); ?></h3>
                            <p class="balance-label">Commission Balance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.2s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon total">
                            <i class="icon-total"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($dashboard_stats['total_commission_earned']); ?></h3>
                            <p class="balance-label">Total Earned</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.3s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon vip">
                            <i class="icon-vip"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount">VIP <?php echo $user_vip['level'] ?? 1; ?></h3>
                            <p class="balance-label"><?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="user-row">
        <!-- Left Column -->
        <div class="user-col-8">
            <!-- Task Overview -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Task Overview</h5>
                    <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-btn user-btn-sm user-btn-outline">View All</a>
                </div>
                <div class="user-card-body">
                    <div class="task-stats">
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $dashboard_stats['tasks_completed_today']; ?></div>
                            <div class="stat-label">Today</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $dashboard_stats['total_tasks_completed']; ?></div>
                            <div class="stat-label">Total Completed</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo count($dashboard_stats['active_tasks']); ?></div>
                            <div class="stat-label">Active Tasks</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $user_vip['max_daily_tasks'] ?? 5; ?></div>
                            <div class="stat-label">Daily Limit</div>
                        </div>
                    </div>

                    <?php if (!empty($dashboard_stats['active_tasks'])): ?>
                        <div class="active-tasks">
                            <h6>Active Tasks</h6>
                            <?php foreach ($dashboard_stats['active_tasks'] as $task): ?>
                                <div class="task-item">
                                    <div class="task-info">
                                        <div class="task-title"><?php echo htmlspecialchars($task['product_name']); ?></div>
                                        <div class="task-amount"><?php echo formatCurrency($task['amount']); ?></div>
                                    </div>
                                    <div class="task-status">
                                        <span class="status-badge status-<?php echo $task['status']; ?>">
                                            <?php echo ucfirst($task['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-active-tasks">
                            <p>No active tasks. <a href="<?php echo BASE_URL; ?>user/tasks/">Start a new task</a></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Recent Transactions</h5>
                    <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-btn user-btn-sm user-btn-outline">View All</a>
                </div>
                <div class="user-card-body">
                    <?php if (!empty($dashboard_stats['recent_transactions'])): ?>
                        <div class="transaction-list">
                            <?php foreach ($dashboard_stats['recent_transactions'] as $transaction): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon">
                                        <i class="icon-<?php echo $transaction['type']; ?>"></i>
                                    </div>
                                    <div class="transaction-details">
                                        <div class="transaction-type"><?php echo ucfirst(str_replace('_', ' ', $transaction['type'])); ?></div>
                                        <div class="transaction-date"><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></div>
                                    </div>
                                    <div class="transaction-amount <?php echo ($transaction['amount'] > 0) ? 'positive' : 'negative'; ?>">
                                        <?php echo ($transaction['amount'] > 0) ? '+' : ''; ?><?php echo formatCurrency($transaction['amount']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-transactions">
                            <p>No recent transactions.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="user-col-4">
            <!-- VIP Progress -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">VIP Progress</h5>
                </div>
                <div class="user-card-body">
                    <div class="vip-current">
                        <div class="vip-level">VIP <?php echo $user_vip['level'] ?? 1; ?></div>
                        <div class="vip-name"><?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?></div>
                    </div>
                    
                    <?php if (isset($dashboard_stats['vip_progress'])): ?>
                        <div class="vip-progress-bar">
                            <div class="progress-bar" style="width: <?php echo $dashboard_stats['vip_progress']['percentage']; ?>%"></div>
                        </div>
                        <div class="vip-progress-text">
                            <?php echo formatCurrency($dashboard_stats['vip_progress']['current']); ?> / 
                            <?php echo formatCurrency($dashboard_stats['vip_progress']['required']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <a href="<?php echo BASE_URL; ?>user/vip/" class="user-btn user-btn-primary user-w-100 user-mt-3">
                        View VIP Benefits
                    </a>
                </div>
            </div>

            <!-- Quick Menu -->
            <div class="user-card quick-menu-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Quick Menu</h5>
                    <p class="user-card-subtitle">Access all platform features</p>
                </div>
                <div class="user-card-body">
                    <div class="quick-menu-grid">
                        <a href="<?php echo BASE_URL; ?>user/team/" class="quick-menu-item">
                            <div class="menu-icon team-icon">
                                <i class="icon-users"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Downline Team</h6>
                                <p>Manage referrals & commissions</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/certificate/" class="quick-menu-item">
                            <div class="menu-icon certificate-icon">
                                <i class="icon-shield-check"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Certificate</h6>
                                <p>Platform verification</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/withdraw/" class="quick-menu-item">
                            <div class="menu-icon withdraw-icon">
                                <i class="icon-arrow-up"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Withdraw</h6>
                                <p>Cash out your earnings</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/deposit/" class="quick-menu-item">
                            <div class="menu-icon deposit-icon">
                                <i class="icon-arrow-down"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Deposit</h6>
                                <p>Add funds to wallet</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/terms/" class="quick-menu-item">
                            <div class="menu-icon terms-icon">
                                <i class="icon-file-text"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Terms & Conditions</h6>
                                <p>Platform rules & policies</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/campaign/" class="quick-menu-item">
                            <div class="menu-icon campaign-icon">
                                <i class="icon-megaphone"></i>
                            </div>
                            <div class="menu-content">
                                <h6>Latest Campaign</h6>
                                <p>Current promotions</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/faq/" class="quick-menu-item">
                            <div class="menu-icon faq-icon">
                                <i class="icon-help-circle"></i>
                            </div>
                            <div class="menu-content">
                                <h6>FAQ</h6>
                                <p>Frequently asked questions</p>
                            </div>
                        </a>

                        <a href="<?php echo BASE_URL; ?>user/about/" class="quick-menu-item">
                            <div class="menu-icon about-icon">
                                <i class="icon-info"></i>
                            </div>
                            <div class="menu-content">
                                <h6>About Us</h6>
                                <p>Company information</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <?php if (!empty($recent_notifications)): ?>
                <div class="user-card user-fade-in">
                    <div class="user-card-header">
                        <h5 class="user-card-title">Recent Notifications</h5>
                    </div>
                    <div class="user-card-body">
                        <div class="notification-list">
                            <?php foreach ($recent_notifications as $notification): ?>
                                <div class="notification-item">
                                    <div class="notification-content">
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                        <div class="notification-time"><?php echo timeAgo($notification['created_at']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Promotional Modal -->
<?php if ($show_welcome_popup): ?>
<div class="modal fade" id="welcomeModal" tabindex="-1" aria-labelledby="welcomeModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 text-center">
                <div class="w-100">
                    <div class="welcome-modal-logo mx-auto mb-3">
                        <?php if (!empty($app_logo)): ?>
                            <img src="<?php echo htmlspecialchars($app_logo); ?>" alt="<?php echo htmlspecialchars($app_name); ?>" class="modal-logo-img">
                        <?php else: ?>
                            <div class="modal-logo-placeholder">K</div>
                        <?php endif; ?>
                    </div>
                    <h4 class="modal-title text-primary fw-bold" id="welcomeModalLabel"><?php echo htmlspecialchars($app_name); ?></h4>
                </div>
                <button type="button" class="btn-close position-absolute top-0 end-0 m-3" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center px-4 pb-4">
                <div class="usdt-highlight mb-3">
                    <span class="usdt-text">USDT <?php echo htmlspecialchars($usdt_multiplier); ?>X</span>
                </div>
                <p class="welcome-message mb-4">
                    <?php echo htmlspecialchars($welcome_bonus_message); ?>
                </p>
                <button type="button" class="btn btn-primary btn-lg px-4" data-bs-dismiss="modal">
                    <i class="bi bi-rocket-takeoff me-2"></i>Get Started
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.welcome-modal-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.modal-logo-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.modal-logo-placeholder {
    color: white;
    font-size: 2rem;
    font-weight: 700;
}

.usdt-highlight {
    background: linear-gradient(135deg, #059669, #10b981);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.usdt-text {
    font-size: 1.25rem;
    font-weight: 700;
}

.welcome-message {
    color: #6b7280;
    line-height: 1.6;
    font-size: 1rem;
}

.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show welcome modal automatically
    const welcomeModal = new bootstrap.Modal(document.getElementById('welcomeModal'));
    welcomeModal.show();
});
</script>
<?php endif; ?>

<?php
// Include footer
include '../includes/user_footer.php';
?>
