<?php
/**
 * Bamboo User Dashboard - Main Dashboard Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: Main user dashboard with balance, VIP status, and quick actions
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user financial data
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get dashboard statistics
$dashboard_stats = [
    'total_tasks_completed' => getTotalTasksCompleted($user_id),
    'tasks_completed_today' => getTasksCompletedToday($user_id),
    'total_commission_earned' => getTotalCommissionEarned($user_id),
    'referral_count' => getReferralCount($user_id),
    'pending_withdrawals' => getPendingWithdrawals($user_id),
    'recent_transactions' => getRecentTransactions($user_id, 4),
    'active_tasks' => getActiveTasks($user_id),
    'vip_progress' => getVipProgress($user_id)
];

// Get recent notifications
$recent_notifications = getRecentNotifications($user_id, 3);

// Page configuration
$page_title = 'Dashboard';
$page_description = 'User dashboard with balance overview and quick actions';
$page_css = 'dashboard.css';
$page_js = 'dashboard.js';

// Check if this is the first login today (for popup)
$show_welcome_popup = false;
$last_login = $current_user['last_login'] ?? null;
if (!$last_login || date('Y-m-d', strtotime($last_login)) !== date('Y-m-d')) {
    $show_welcome_popup = true;
    // Update last login
    executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user_id]);
}

// Get app settings for popup
$app_logo = getAppSetting('app_logo', '');
$app_name = getAppSetting('app_name', APP_NAME);
$usdt_multiplier = getAppSetting('usdt_multiplier_x', '10');
$welcome_bonus_message = getAppSetting('welcome_bonus_message', 'During the anniversary celebration, New users can get a bonus for the first time to complete group tasks.');

// Get notification banner message
$notification_banner = fetchRow("SELECT message FROM notifications_banner WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1");
$banner_message = $notification_banner['message'] ?? 'Welcome to Kompyte! 🎉 USDT 10X bonus available for new users completing group tasks. Start earning today!';

// USDT notification for login
$usdt_notification = "🚀 USDT Earning Alert: Complete daily tasks to earn up to " . $usdt_multiplier . "X bonus rewards! Your current VIP level: " . ($user_vip['name'] ?? 'Bronze');

// Include header
include '../includes/user_header.php';
?>



<!-- USDT Notification Modal -->
<div class="modal fade" id="usdtNotificationModal" tabindex="-1" aria-labelledby="usdtModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content usdt-modal-content">
            <div class="modal-header usdt-modal-header border-0">
                <div class="w-100 text-center">
                    <div class="usdt-modal-icon">💰</div>
                    <h4 class="modal-title usdt-modal-title" id="usdtModalLabel">USDT <?php echo htmlspecialchars($usdt_multiplier); ?>X</h4>
                    <p class="usdt-modal-subtitle">Anniversary Celebration</p>
                </div>
                <button type="button" class="btn-close usdt-modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body usdt-modal-body text-center">
                <div class="usdt-highlight-box">
                    <div class="usdt-multiplier"><?php echo htmlspecialchars($usdt_multiplier); ?>X</div>
                    <div class="usdt-label">USDT Bonus</div>
                </div>
                <p class="usdt-message">
                    <?php echo htmlspecialchars($welcome_bonus_message); ?>
                </p>
                <div class="usdt-features">
                    <div class="feature-item">
                        <i class="icon-check"></i>
                        <span>Instant Rewards</span>
                    </div>
                    <div class="feature-item">
                        <i class="icon-check"></i>
                        <span><?php echo htmlspecialchars($usdt_multiplier); ?>X Multiplier</span>
                    </div>
                    <div class="feature-item">
                        <i class="icon-check"></i>
                        <span>Group Bonuses</span>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-lg usdt-action-btn" data-bs-dismiss="modal">
                    <i class="icon-rocket"></i>
                    Start Earning Now
                </button>
            </div>
        </div>
    </div>
</div>

<div class="user-container">
    <!-- Welcome Section -->
    <div class="dashboard-welcome user-fade-in">
        <div class="welcome-content">
            <h1 class="welcome-title">Welcome back, <?php echo htmlspecialchars($current_user['username']); ?>!</h1>
            <p class="welcome-subtitle">Here's your account overview and recent activity</p>
        </div>
        <div class="welcome-actions">
            <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-btn user-btn-primary">
                <i class="icon-tasks"></i>
                Start Tasks
            </a>
            <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-outline">
                <i class="icon-deposit"></i>
                Deposit
            </a>
        </div>
    </div>

    <!-- Balance Overview Cards -->
    <div class="user-row user-mb-4">
        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon">
                            <i class="icon-wallet"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($user_balance['balance'] ?? 0); ?></h3>
                            <p class="balance-label">Main Balance</p>
                        </div>
                    </div>
                    <div class="balance-actions">
                        <button class="refresh-balance user-btn user-btn-sm" title="Refresh">
                            <i class="icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.1s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon commission">
                            <i class="icon-commission"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($user_balance['commission_balance'] ?? 0); ?></h3>
                            <p class="balance-label">Commission Balance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.2s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon total">
                            <i class="icon-total"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount"><?php echo formatCurrency($dashboard_stats['total_commission_earned']); ?></h3>
                            <p class="balance-label">Total Earned</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-col-3">
            <div class="user-card balance-card user-slide-in" style="animation-delay: 0.3s;">
                <div class="user-card-body">
                    <div class="balance-info">
                        <div class="balance-icon vip">
                            <i class="icon-vip"></i>
                        </div>
                        <div class="balance-details">
                            <h3 class="balance-amount">VIP <?php echo $user_vip['level'] ?? 1; ?></h3>
                            <p class="balance-label"><?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="user-row">
        <!-- Left Column -->
        <div class="user-col-8">
            <!-- Task Overview -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Task Overview</h5>
                    <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-btn user-btn-sm user-btn-outline">View All</a>
                </div>
                <div class="user-card-body">
                    <div class="task-stats">
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $dashboard_stats['tasks_completed_today']; ?></div>
                            <div class="stat-label">Today</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $dashboard_stats['total_tasks_completed']; ?></div>
                            <div class="stat-label">Total Completed</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo count($dashboard_stats['active_tasks']); ?></div>
                            <div class="stat-label">Active Tasks</div>
                        </div>
                        <div class="task-stat-item">
                            <div class="stat-number"><?php echo $user_vip['max_daily_tasks'] ?? 5; ?></div>
                            <div class="stat-label">Daily Limit</div>
                        </div>
                    </div>

                    <?php if (!empty($dashboard_stats['active_tasks'])): ?>
                        <div class="active-tasks">
                            <h6>Active Tasks</h6>
                            <?php foreach ($dashboard_stats['active_tasks'] as $task): ?>
                                <div class="task-item">
                                    <div class="task-info">
                                        <div class="task-title"><?php echo htmlspecialchars($task['product_name']); ?></div>
                                        <div class="task-amount"><?php echo formatCurrency($task['amount']); ?></div>
                                    </div>
                                    <div class="task-status">
                                        <span class="status-badge status-<?php echo $task['status']; ?>">
                                            <?php echo ucfirst($task['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-active-tasks">
                            <p>No active tasks. <a href="<?php echo BASE_URL; ?>user/tasks/">Start a new task</a></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Recent Transactions</h5>
                    <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-btn user-btn-sm user-btn-outline">View All</a>
                </div>
                <div class="user-card-body">
                    <?php if (!empty($dashboard_stats['recent_transactions'])): ?>
                        <div class="transaction-table-container">
                            <div class="transaction-table">
                                <?php foreach ($dashboard_stats['recent_transactions'] as $index => $transaction): ?>
                                    <div class="transaction-row <?php echo ($index % 2 === 0) ? 'row-white' : 'row-light'; ?>">
                                        <div class="transaction-info">
                                            <div class="transaction-icon">
                                                <i class="icon-<?php echo $transaction['type']; ?>"></i>
                                            </div>
                                            <div class="transaction-details">
                                                <div class="transaction-type"><?php echo ucfirst(str_replace('_', ' ', $transaction['type'])); ?></div>
                                                <div class="transaction-date"><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></div>
                                            </div>
                                        </div>
                                        <div class="transaction-amount <?php echo ($transaction['amount'] > 0) ? 'positive' : 'negative'; ?>">
                                            <?php echo ($transaction['amount'] > 0) ? '+' : ''; ?><?php echo formatCurrency($transaction['amount']); ?>
                                        </div>
                                        <div class="transaction-status">
                                            <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="no-transactions">
                            <div class="no-transactions-icon">
                                <i class="icon-transactions"></i>
                            </div>
                            <p>No recent transactions found</p>
                            <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-primary user-btn-sm">Make First Deposit</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="user-col-4">
            <!-- VIP Progress -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">VIP Progress</h5>
                </div>
                <div class="user-card-body">
                    <div class="vip-current">
                        <div class="vip-level">VIP <?php echo $user_vip['level'] ?? 1; ?></div>
                        <div class="vip-name"><?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?></div>
                    </div>
                    
                    <?php if (isset($dashboard_stats['vip_progress'])): ?>
                        <div class="vip-progress-bar">
                            <div class="progress-bar" style="width: <?php echo $dashboard_stats['vip_progress']['percentage']; ?>%"></div>
                        </div>
                        <div class="vip-progress-text">
                            <?php echo formatCurrency($dashboard_stats['vip_progress']['current']); ?> / 
                            <?php echo formatCurrency($dashboard_stats['vip_progress']['required']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <a href="<?php echo BASE_URL; ?>user/vip/" class="user-btn user-btn-primary user-w-100 user-mt-3">
                        View VIP Benefits
                    </a>
                </div>
            </div>

            <!-- Quick Menu -->
            <div class="user-card quick-menu-card user-fade-in">
                <div class="user-card-header">
                    <h5 class="user-card-title">Quick Actions</h5>
                    <p class="user-card-subtitle">Access essential platform features</p>
                </div>
                <div class="user-card-body">
                    <!-- Primary Actions -->
                    <div class="quick-menu-section">
                        <h6 class="menu-section-title">Financial</h6>
                        <div class="quick-menu-grid primary-actions">
                            <a href="<?php echo BASE_URL; ?>user/deposit/" class="quick-menu-item primary-item">
                                <div class="menu-icon deposit-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L12 22M5 9L12 2L19 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Deposit</h6>
                                    <p>Add funds</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/withdraw/" class="quick-menu-item primary-item">
                                <div class="menu-icon withdraw-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 22L12 2M19 15L12 22L5 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Withdraw</h6>
                                    <p>Cash out</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/team/" class="quick-menu-item primary-item">
                                <div class="menu-icon team-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.7007C21.7033 16.0473 20.9999 15.5885 20.2 15.3949" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M16 3.13C16.8003 3.32352 17.5037 3.78226 18.0098 4.43563C18.5159 5.089 18.8004 5.89946 18.8004 6.735C18.8004 7.57054 18.5159 8.381 18.0098 9.03437C17.5037 9.68774 16.8003 10.1465 16 10.34" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Team</h6>
                                    <p>Referrals</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/certificate/" class="quick-menu-item primary-item">
                                <div class="menu-icon certificate-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Certificate</h6>
                                    <p>Verification</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Secondary Actions -->
                    <div class="quick-menu-section">
                        <h6 class="menu-section-title">Support & Information</h6>
                        <div class="quick-menu-grid secondary-actions">
                            <a href="<?php echo BASE_URL; ?>user/faq/" class="quick-menu-item secondary-item">
                                <div class="menu-icon faq-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>FAQ</h6>
                                    <p>Help</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/terms/" class="quick-menu-item secondary-item">
                                <div class="menu-icon terms-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Terms</h6>
                                    <p>Conditions</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/campaign/" class="quick-menu-item secondary-item">
                                <div class="menu-icon campaign-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>Campaign</h6>
                                    <p>Latest</p>
                                </div>
                            </a>

                            <a href="<?php echo BASE_URL; ?>user/about/" class="quick-menu-item secondary-item">
                                <div class="menu-icon about-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M12 16V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M12 8H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="menu-content">
                                    <h6>About</h6>
                                    <p>Company</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <?php if (!empty($recent_notifications)): ?>
                <div class="user-card user-fade-in">
                    <div class="user-card-header">
                        <h5 class="user-card-title">Recent Notifications</h5>
                    </div>
                    <div class="user-card-body">
                        <div class="notification-list">
                            <?php foreach ($recent_notifications as $notification): ?>
                                <div class="notification-item">
                                    <div class="notification-content">
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                        <div class="notification-time"><?php echo timeAgo($notification['created_at']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>



<?php
// Include footer
include '../includes/user_footer.php';
?>
