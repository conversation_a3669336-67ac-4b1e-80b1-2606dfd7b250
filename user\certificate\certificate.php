<?php
/**
 * Bamboo User Dashboard - Certificate Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: App certificate and verification
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get certificate PDF from admin settings
$certificate_filename = getAppSetting('app_certificate', '');
$certificate_pdf = '';
if (!empty($certificate_filename)) {
    $certificate_pdf = BASE_URL . 'uploads/certificates/' . $certificate_filename;
}

// Page configuration
$page_title = 'Certificate';
$page_description = 'App certificate and verification information';
$page_css = 'certificate.css';
$page_js = 'certificate.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="page-header user-fade-in">
        <h1 class="page-title">App Certificate</h1>
        <p class="page-subtitle">Verification and security information</p>
    </div>

    <!-- Certificate PDF Preview -->
    <?php if ($certificate_pdf): ?>
    <div class="user-card certificate-card user-fade-in">
        <div class="user-card-body">
            <div class="pdf-preview-container">
                <iframe src="<?php echo htmlspecialchars($certificate_pdf); ?>"
                        class="certificate-pdf-viewer"
                        frameborder="0">
                    <p>Your browser does not support PDF viewing.
                       <a href="<?php echo htmlspecialchars($certificate_pdf); ?>" target="_blank">Download the certificate</a>
                    </p>
                </iframe>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="user-card certificate-card user-fade-in">
        <div class="user-card-body">
            <div class="no-certificate">
                <div class="no-cert-icon">
                    <i class="icon-file-text"></i>
                </div>
                <h4>No Certificate Available</h4>
                <p>The platform certificate will be displayed here once uploaded by the administrator.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Security Features -->
    <div class="user-card security-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Security Features</h5>
        </div>
        <div class="user-card-body">
            <div class="security-grid">
                <div class="security-item">
                    <div class="security-icon ssl">
                        <i class="icon-lock"></i>
                    </div>
                    <div class="security-content">
                        <h6>SSL Encryption</h6>
                        <p>All data transmitted is protected with 256-bit SSL encryption</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon secure">
                        <i class="icon-shield"></i>
                    </div>
                    <div class="security-content">
                        <h6>Secure Payments</h6>
                        <p>USDT transactions are processed through secure blockchain networks</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon verified">
                        <i class="icon-check-circle"></i>
                    </div>
                    <div class="security-content">
                        <h6>Verified Platform</h6>
                        <p>Officially registered and compliant with digital platform regulations</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon privacy">
                        <i class="icon-eye-off"></i>
                    </div>
                    <div class="security-content">
                        <h6>Privacy Protection</h6>
                        <p>Your personal information is protected and never shared with third parties</p>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Download Certificate -->
    <div class="user-card download-card user-fade-in">
        <div class="user-card-body">
            <div class="download-content">
                <div class="download-info">
                    <h5>Download Certificate</h5>
                    <p>Download the official platform certificate for your records</p>
                </div>
                <div class="download-actions">
                    <?php if ($certificate_pdf): ?>
                        <a href="<?php echo htmlspecialchars($certificate_pdf); ?>" download class="user-btn user-btn-primary">
                            <i class="icon-download"></i>
                            Download PDF
                        </a>
                    <?php else: ?>
                        <button class="user-btn user-btn-primary" onclick="downloadCertificate()">
                            <i class="icon-download"></i>
                            Download PDF
                        </button>
                    <?php endif; ?>
                    <button class="user-btn user-btn-outline" onclick="shareCertificate()">
                        <i class="icon-share"></i>
                        Share
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadCertificate() {
    <?php if ($certificate_pdf): ?>
        window.open('<?php echo htmlspecialchars($certificate_pdf); ?>', '_blank');
    <?php else: ?>
        UserApp.showNotification('Certificate download will be available soon', 'info');
    <?php endif; ?>
}

function shareCertificate() {
    const certificateInfo = `Kompyte Platform Certificate
    
✅ Verified and Secure Platform
🏢 Company: Notepadsly
📋 Certificate ID: KOMP-2025-CERT-001
🔒 SSL Encrypted & Compliant
💰 Secure USDT Transactions

Join the verified platform: ${window.location.origin}`;

    if (navigator.share) {
        navigator.share({
            title: 'Kompyte Platform Certificate',
            text: certificateInfo
        });
    } else {
        navigator.clipboard.writeText(certificateInfo).then(() => {
            UserApp.showNotification('Certificate info copied to clipboard!', 'success');
        });
    }
}
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
