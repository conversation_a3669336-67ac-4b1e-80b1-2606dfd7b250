<?php
/**
 * Bamboo User Dashboard - Certificate Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: App certificate and verification
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Page configuration
$page_title = 'Certificate';
$page_description = 'App certificate and verification information';
$page_css = 'certificate.css';
$page_js = 'certificate.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="page-header user-fade-in">
        <h1 class="page-title">App Certificate</h1>
        <p class="page-subtitle">Verification and security information</p>
    </div>

    <!-- Certificate Card -->
    <div class="user-card certificate-card user-fade-in">
        <div class="user-card-body">
            <div class="certificate-content">
                <div class="certificate-badge">
                    <div class="badge-icon">
                        <i class="icon-shield-check"></i>
                    </div>
                    <h3>Verified Platform</h3>
                    <p>Kompyte is a certified and secure task management platform</p>
                </div>
                
                <div class="certificate-details">
                    <div class="detail-item">
                        <span class="detail-label">Platform Name:</span>
                        <span class="detail-value">Kompyte</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Company:</span>
                        <span class="detail-value">Notepadsly</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Version:</span>
                        <span class="detail-value">1.0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Certificate ID:</span>
                        <span class="detail-value">KOMP-2025-CERT-001</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Issue Date:</span>
                        <span class="detail-value">January 1, 2025</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Valid Until:</span>
                        <span class="detail-value">December 31, 2025</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Features -->
    <div class="user-card security-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Security Features</h5>
        </div>
        <div class="user-card-body">
            <div class="security-grid">
                <div class="security-item">
                    <div class="security-icon ssl">
                        <i class="icon-lock"></i>
                    </div>
                    <div class="security-content">
                        <h6>SSL Encryption</h6>
                        <p>All data transmitted is protected with 256-bit SSL encryption</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon secure">
                        <i class="icon-shield"></i>
                    </div>
                    <div class="security-content">
                        <h6>Secure Payments</h6>
                        <p>USDT transactions are processed through secure blockchain networks</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon verified">
                        <i class="icon-check-circle"></i>
                    </div>
                    <div class="security-content">
                        <h6>Verified Platform</h6>
                        <p>Officially registered and compliant with digital platform regulations</p>
                    </div>
                </div>
                
                <div class="security-item">
                    <div class="security-icon privacy">
                        <i class="icon-eye-off"></i>
                    </div>
                    <div class="security-content">
                        <h6>Privacy Protection</h6>
                        <p>Your personal information is protected and never shared with third parties</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Information -->
    <div class="user-card compliance-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Compliance & Regulations</h5>
        </div>
        <div class="user-card-body">
            <div class="compliance-content">
                <div class="compliance-item">
                    <h6>Financial Compliance</h6>
                    <p>Kompyte operates in full compliance with digital asset regulations and maintains proper financial licensing for USDT transactions.</p>
                </div>
                
                <div class="compliance-item">
                    <h6>Data Protection</h6>
                    <p>We adhere to international data protection standards including GDPR compliance for user privacy and data security.</p>
                </div>
                
                <div class="compliance-item">
                    <h6>Platform Security</h6>
                    <p>Regular security audits are conducted to ensure the platform maintains the highest security standards for user protection.</p>
                </div>
                
                <div class="compliance-item">
                    <h6>Transparency</h6>
                    <p>All platform operations are transparent with clear terms of service and user agreements available for review.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Download Certificate -->
    <div class="user-card download-card user-fade-in">
        <div class="user-card-body">
            <div class="download-content">
                <div class="download-info">
                    <h5>Download Certificate</h5>
                    <p>Download the official platform certificate for your records</p>
                </div>
                <div class="download-actions">
                    <button class="user-btn user-btn-primary" onclick="downloadCertificate()">
                        <i class="icon-download"></i>
                        Download PDF
                    </button>
                    <button class="user-btn user-btn-outline" onclick="shareCertificate()">
                        <i class="icon-share"></i>
                        Share
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadCertificate() {
    UserApp.showNotification('Certificate download will be available soon', 'info');
}

function shareCertificate() {
    const certificateInfo = `Kompyte Platform Certificate
    
✅ Verified and Secure Platform
🏢 Company: Notepadsly
📋 Certificate ID: KOMP-2025-CERT-001
🔒 SSL Encrypted & Compliant
💰 Secure USDT Transactions

Join the verified platform: ${window.location.origin}`;

    if (navigator.share) {
        navigator.share({
            title: 'Kompyte Platform Certificate',
            text: certificateInfo
        });
    } else {
        navigator.clipboard.writeText(certificateInfo).then(() => {
            UserApp.showNotification('Certificate info copied to clipboard!', 'success');
        });
    }
}
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
