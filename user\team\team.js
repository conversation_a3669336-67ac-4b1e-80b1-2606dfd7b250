/**
 * Bamboo User Dashboard - Team Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Team management functionality
 */

// Team management object
const TeamManager = {
    init: function() {
        this.bindEvents();
        this.initializeAnimations();
        console.log('TeamManager initialized');
    },

    bindEvents: function() {
        // Copy buttons
        $('[data-copy]').on('click', this.handleCopy.bind(this));
        
        // Share buttons
        $('.share-btn').on('click', this.handleShare.bind(this));
        
        // Refresh team data
        $('.refresh-team').on('click', this.refreshTeamData.bind(this));
    },

    initializeAnimations: function() {
        // Animate stat numbers
        $('.stat-number').each(function() {
            const $this = $(this);
            const text = $this.text();
            
            // Check if it's a number (not USDT amount)
            if (/^\d+$/.test(text)) {
                const finalValue = parseInt(text);
                $this.text('0');
                
                $({ value: 0 }).animate({ value: finalValue }, {
                    duration: 1500,
                    step: function() {
                        $this.text(Math.floor(this.value));
                    },
                    complete: function() {
                        $this.text(finalValue);
                    }
                });
            }
        });

        // Stagger animation for table rows
        $('.referral-row').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateY(0)');
        });
    },

    handleCopy: function(e) {
        e.preventDefault();
        const $button = $(e.currentTarget);
        const textToCopy = $button.data('copy');
        
        navigator.clipboard.writeText(textToCopy).then(() => {
            // Change button text temporarily
            const originalText = $button.html();
            $button.html('<i class="icon-check"></i> Copied!');
            $button.addClass('user-btn-success').removeClass('user-btn-primary');
            
            setTimeout(() => {
                $button.html(originalText);
                $button.addClass('user-btn-primary').removeClass('user-btn-success');
            }, 2000);
            
            UserApp.showNotification('Copied to clipboard!', 'success', 2000);
        }).catch(() => {
            UserApp.showNotification('Failed to copy to clipboard', 'error');
        });
    },

    handleShare: function(e) {
        e.preventDefault();
        const inviteLink = window.location.origin + '/user/register/?ref=' + UserApp.config.invitationCode;
        
        if (navigator.share) {
            navigator.share({
                title: 'Join Kompyte',
                text: 'Join me on Kompyte and start earning USDT through task completion!',
                url: inviteLink
            }).catch(console.error);
        } else {
            // Fallback to copy
            navigator.clipboard.writeText(inviteLink).then(() => {
                UserApp.showNotification('Invitation link copied to clipboard!', 'success');
            });
        }
    },

    refreshTeamData: function(e) {
        e.preventDefault();
        const $button = $(e.currentTarget);
        
        $button.addClass('user-loading').prop('disabled', true);
        
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/team.php',
            method: 'GET',
            data: { action: 'refresh_stats' },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.updateTeamStats(response.data);
                    UserApp.showNotification('Team data refreshed', 'success');
                } else {
                    UserApp.showNotification('Failed to refresh data', 'error');
                }
            },
            error: () => {
                UserApp.showNotification('Network error occurred', 'error');
            },
            complete: () => {
                $button.removeClass('user-loading').prop('disabled', false);
            }
        });
    },

    updateTeamStats: function(data) {
        // Update stat numbers with animation
        Object.keys(data).forEach(key => {
            const $element = $(`[data-stat="${key}"]`);
            if ($element.length) {
                const currentValue = parseInt($element.text()) || 0;
                const newValue = data[key];
                
                if (currentValue !== newValue) {
                    $({ value: currentValue }).animate({ value: newValue }, {
                        duration: 1000,
                        step: function() {
                            $element.text(Math.floor(this.value));
                        },
                        complete: function() {
                            $element.text(newValue);
                        }
                    });
                }
            }
        });
    },

    generateShareMessage: function() {
        return `🚀 Join me on Kompyte and start earning USDT!
        
💰 Complete simple tasks and earn real money
🎯 Professional task management platform
📈 Multiple VIP levels with increasing benefits
👥 Build your team and earn referral commissions

Use my invitation code: ${UserApp.config.invitationCode}
Or click this link: ${window.location.origin}/user/register/?ref=${UserApp.config.invitationCode}

Start earning today! 💎`;
    }
};

// Global functions
function copyInviteCode() {
    const code = UserApp.config.invitationCode || document.querySelector('[data-copy]').dataset.copy;
    navigator.clipboard.writeText(code).then(() => {
        UserApp.showNotification('Invitation code copied to clipboard!', 'success');
    });
}

function shareInvitation() {
    const message = TeamManager.generateShareMessage();
    
    if (navigator.share) {
        navigator.share({
            title: 'Join Kompyte',
            text: message
        }).catch(console.error);
    } else {
        navigator.clipboard.writeText(message).then(() => {
            UserApp.showNotification('Invitation message copied to clipboard!', 'success');
        });
    }
}

// Initialize when page is ready
function initializePage() {
    TeamManager.init();
}

// Export for global access
window.TeamManager = TeamManager;
window.copyInviteCode = copyInviteCode;
window.shareInvitation = shareInvitation;
