<?php
/**
 * Setup sample data for testing
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

echo "<h1>Sample Data Setup</h1>";

try {
    $db = getDB();
    
    // Check if users already exist
    $existing_users = fetchAll("SELECT COUNT(*) as count FROM users");
    $user_count = $existing_users[0]['count'] ?? 0;
    
    if ($user_count > 0) {
        echo "<p>Users already exist in database ($user_count users found).</p>";
    } else {
        echo "<p>No users found. Inserting sample data...</p>";
        
        // Create password hash for 'password'
        $password_hash = password_hash('password', PASSWORD_DEFAULT);
        $pin_hash = password_hash('1234', PASSWORD_DEFAULT);
        
        // Insert sample users
        $sql = "INSERT INTO users (username, phone, email, password_hash, withdrawal_pin_hash, gender, invitation_code, balance, commission_balance, frozen_balance, total_deposited, total_withdrawn, total_commission_earned, vip_level, tasks_completed_today, last_task_date, status, email_verified, phone_verified, referral_count, last_login, login_count, created_at, updated_at) VALUES 
        ('alice', '1234567890', '<EMAIL>', ?, ?, 'female', 'ALICE01', 1000.00, 100.00, 0.00, 2000.00, 500.00, 200.00, 2, 2, CURDATE(), 'active', 1, 1, 0, NOW(), 5, NOW(), NOW()),
        ('bob', '2345678901', '<EMAIL>', ?, ?, 'male', 'BOB01', 1500.00, 150.00, 0.00, 3000.00, 1000.00, 300.00, 3, 1, CURDATE(), 'active', 1, 1, 1, NOW(), 10, NOW(), NOW()),
        ('charlie', '3456789012', '<EMAIL>', ?, ?, 'male', 'CHARLIE01', 500.00, 50.00, 0.00, 1000.00, 200.00, 100.00, 1, 0, CURDATE(), 'active', 1, 1, 0, NOW(), 3, NOW(), NOW())";
        
        $result = executeQuery($sql, [
            $password_hash, $pin_hash,
            $password_hash, $pin_hash,
            $password_hash, $pin_hash
        ]);
        
        if ($result) {
            echo "<p><strong>Sample users created successfully!</strong></p>";
            echo "<ul>";
            echo "<li>Username: alice, Password: password, PIN: 1234</li>";
            echo "<li>Username: bob, Password: password, PIN: 1234</li>";
            echo "<li>Username: charlie, Password: password, PIN: 1234</li>";
            echo "</ul>";
        } else {
            echo "<p><strong>Failed to create sample users.</strong></p>";
        }
    }
    
    // Check VIP levels
    $vip_levels = fetchAll("SELECT COUNT(*) as count FROM vip_levels");
    $vip_count = $vip_levels[0]['count'] ?? 0;
    
    if ($vip_count == 0) {
        echo "<p>No VIP levels found. Creating default VIP levels...</p>";
        
        $vip_sql = "INSERT INTO vip_levels (level, name, min_balance, max_daily_tasks, commission_rate, benefits, created_at, updated_at) VALUES
        (1, 'Bronze', 0.00, 5, 5.00, 'Basic benefits', NOW(), NOW()),
        (2, 'Silver', 1000.00, 10, 7.50, 'Enhanced benefits', NOW(), NOW()),
        (3, 'Gold', 5000.00, 15, 10.00, 'Premium benefits', NOW(), NOW()),
        (4, 'Platinum', 10000.00, 20, 12.50, 'Elite benefits', NOW(), NOW()),
        (5, 'Diamond', 25000.00, 30, 15.00, 'Ultimate benefits', NOW(), NOW())";
        
        $result = executeQuery($vip_sql);
        
        if ($result) {
            echo "<p><strong>VIP levels created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create VIP levels.</strong></p>";
        }
    } else {
        echo "<p>VIP levels already exist ($vip_count levels found).</p>";
    }
    
    // Check products
    $products = fetchAll("SELECT COUNT(*) as count FROM products");
    $product_count = $products[0]['count'] ?? 0;
    
    if ($product_count == 0) {
        echo "<p>No products found. Creating sample products...</p>";

        $product_sql = "INSERT INTO products (name, description, price, commission_rate, min_vip_level, max_vip_level, category_id, status, created_at, updated_at) VALUES
        ('iPhone 15 Pro Max', 'Latest Apple smartphone with advanced features', 1299.00, 8.50, 1, 5, 1, 'active', NOW(), NOW()),
        ('Samsung Galaxy S24 Ultra', 'Premium Android smartphone with S Pen', 1199.00, 8.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('MacBook Pro M3', 'Professional laptop for creative work', 1999.00, 12.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Sony WH-1000XM5', 'Noise-canceling wireless headphones', 399.00, 15.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('iPad Pro 12.9', 'Professional tablet with M2 chip', 1099.00, 10.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Apple Watch Series 9', 'Advanced smartwatch with health features', 399.00, 12.50, 1, 5, 1, 'active', NOW(), NOW()),
        ('Dell XPS 13', 'Ultra-portable Windows laptop', 999.00, 9.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Nintendo Switch OLED', 'Portable gaming console', 349.00, 18.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('AirPods Pro 2', 'Premium wireless earbuds', 249.00, 20.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('Tesla Model Y', 'Electric SUV with autopilot', 52990.00, 5.00, 4, 5, 1, 'active', NOW(), NOW()),
        ('Canon EOS R5', 'Professional mirrorless camera', 3899.00, 7.50, 3, 5, 1, 'active', NOW(), NOW()),
        ('PlayStation 5', 'Next-gen gaming console', 499.00, 16.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Microsoft Surface Pro 9', '2-in-1 tablet and laptop', 999.00, 9.50, 2, 5, 1, 'active', NOW(), NOW()),
        ('Dyson V15 Detect', 'Cordless vacuum cleaner', 749.00, 14.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Rolex Submariner', 'Luxury diving watch', 8100.00, 4.50, 5, 5, 1, 'active', NOW(), NOW()),
        ('LG OLED C3 55', '55-inch OLED smart TV', 1499.00, 8.50, 3, 5, 1, 'active', NOW(), NOW()),
        ('Bose QuietComfort 45', 'Wireless noise-canceling headphones', 329.00, 17.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('Google Pixel 8 Pro', 'AI-powered Android smartphone', 999.00, 11.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('KitchenAid Stand Mixer', 'Professional kitchen appliance', 379.00, 19.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('Fitbit Charge 6', 'Advanced fitness tracker', 159.99, 25.00, 1, 5, 1, 'active', NOW(), NOW())";

        $result = executeQuery($product_sql);

        if ($result) {
            echo "<p><strong>20 sample products created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create sample products.</strong></p>";
        }
    } else {
        echo "<p>Products already exist ($product_count products found).</p>";
    }
    
    // Check product categories
    $categories = fetchAll("SELECT COUNT(*) as count FROM product_categories");
    $category_count = $categories[0]['count'] ?? 0;
    
    if ($category_count == 0) {
        echo "<p>No product categories found. Creating default category...</p>";
        
        $category_sql = "INSERT INTO product_categories (name, description, status, created_at, updated_at) VALUES
        ('General', 'General product category', 'active', NOW(), NOW())";
        
        $result = executeQuery($category_sql);
        
        if ($result) {
            echo "<p><strong>Product category created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create product category.</strong></p>";
        }
    } else {
        echo "<p>Product categories already exist ($category_count categories found).</p>";
    }

    // Check app settings
    $settings = fetchAll("SELECT COUNT(*) as count FROM settings");
    $settings_count = $settings[0]['count'] ?? 0;

    if ($settings_count == 0) {
        echo "<p>No app settings found. Creating default settings...</p>";

        $settings_sql = "INSERT INTO settings (setting_key, setting_value, created_at, updated_at) VALUES
        ('app_name', 'Bamboo', NOW(), NOW()),
        ('app_logo_url', '', NOW(), NOW()),
        ('usdt_multiplier_x', '10', NOW(), NOW()),
        ('welcome_bonus_message', 'During the anniversary celebration, New users can get a bonus for the first time to complete group tasks.', NOW(), NOW()),
        ('min_wallet_balance_for_orders', '100', NOW(), NOW()),
        ('opening_hour', '9', NOW(), NOW()),
        ('closing_hour', '21', NOW(), NOW()),
        ('min_withdrawal_amount', '100', NOW(), NOW()),
        ('company_name', 'Notepadsly', NOW(), NOW())";

        $result = executeQuery($settings_sql);

        if ($result) {
            echo "<p><strong>App settings created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create app settings.</strong></p>";
        }
    } else {
        echo "<p>App settings already exist ($settings_count settings found).</p>";
    }

    // Check notifications banner
    $notifications = fetchAll("SELECT COUNT(*) as count FROM notifications_banner");
    $notifications_count = $notifications[0]['count'] ?? 0;

    if ($notifications_count == 0) {
        echo "<p>No notification banners found. Creating default banner...</p>";

        $banner_sql = "INSERT INTO notifications_banner (message, is_active, created_at) VALUES
        ('Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!', 1, NOW())";

        $result = executeQuery($banner_sql);

        if ($result) {
            echo "<p><strong>Notification banner created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create notification banner.</strong></p>";
        }
    } else {
        echo "<p>Notification banners already exist ($notifications_count banners found).</p>";
    }

    echo "<hr>";
    echo "<h2>Setup Complete!</h2>";
    echo "<p>You can now test the dashboard with these credentials:</p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> alice, <strong>Password:</strong> password</li>";
    echo "<li><strong>Username:</strong> bob, <strong>Password:</strong> password</li>";
    echo "<li><strong>Username:</strong> charlie, <strong>Password:</strong> password</li>";
    echo "</ul>";
    echo "<p><a href='login/login.php'>Go to Login Page</a></p>";
    echo "<p><a href='test-db.php'>View Database Status</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error: " . $e->getMessage() . "</h2>";
}
?>
