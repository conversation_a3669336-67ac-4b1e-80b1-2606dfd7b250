<?php
/**
 * Bamboo User Dashboard - Notifications API
 * Company: Notepadsly
 * Version: 1.0
 * Description: API endpoint for user notifications
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

// Include required files
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON content type
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

try {
    $user_id = getCurrentUserId();
    $action = $_GET['action'] ?? $_POST['action'] ?? 'recent';
    
    switch ($action) {
        case 'recent':
            // Get recent notifications
            $limit = intval($_GET['limit'] ?? 3);
            $notifications = getRecentNotifications($user_id, $limit);
            
            // Format notifications for display
            $formatted_notifications = [];
            foreach ($notifications as $notification) {
                $formatted_notifications[] = [
                    'id' => $notification['id'],
                    'title' => $notification['title'] ?? 'Notification',
                    'message' => $notification['message'],
                    'type' => $notification['type'] ?? 'info',
                    'is_read' => $notification['is_read'] ?? 0,
                    'created_at' => $notification['created_at'],
                    'time_ago' => timeAgo($notification['created_at'])
                ];
            }
            
            $response = [
                'success' => true,
                'data' => $formatted_notifications
            ];
            break;
            
        case 'mark_read':
            // Mark notification as read
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }
            
            // Validate CSRF token
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token');
            }
            
            $notification_id = intval($_POST['id'] ?? 0);
            
            if ($notification_id <= 0) {
                throw new Exception('Invalid notification ID');
            }
            
            // Update notification
            $sql = "UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?";
            $result = executeQuery($sql, [$notification_id, $user_id]);
            
            if (!$result) {
                throw new Exception('Failed to mark notification as read');
            }
            
            $response = [
                'success' => true,
                'message' => 'Notification marked as read'
            ];
            break;
            
        case 'mark_all_read':
            // Mark all notifications as read
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }
            
            // Validate CSRF token
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token');
            }
            
            $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
            $result = executeQuery($sql, [$user_id]);
            
            if (!$result) {
                throw new Exception('Failed to mark notifications as read');
            }
            
            $response = [
                'success' => true,
                'message' => 'All notifications marked as read'
            ];
            break;
            
        case 'unread_count':
            // Get unread notifications count
            $count = getUnreadNotificationsCount($user_id);
            
            $response = [
                'success' => true,
                'data' => [
                    'unread_count' => $count
                ]
            ];
            break;
            
        default:
            throw new Exception('Invalid action specified');
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log error
    logError('Notifications API error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
