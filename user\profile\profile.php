<?php
/**
 * Bamboo User Application - Profile Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: User profile management page
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get user balance and statistics
$user_balance = getUserBalance($user_id);
$user_vip = getUserVipLevel($user_id);

// Get user statistics
$user_stats = [
    'total_deposits' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'deposit' AND status = 'completed'", [$user_id]),
    'total_withdrawals' => fetchValue("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'withdrawal' AND status = 'completed'", [$user_id]),
    'total_commissions' => fetchValue("SELECT COALESCE(SUM(commission_amount), 0) FROM user_commissions WHERE user_id = ?", [$user_id]),
    'total_tasks' => fetchValue("SELECT COUNT(*) FROM user_tasks WHERE user_id = ? AND status = 'completed'", [$user_id]),
    'referral_count' => fetchValue("SELECT COUNT(*) FROM users WHERE referrer_id = ?", [$user_id])
];

// Get recent transactions (last 10)
$recent_transactions = fetchAll("
    SELECT t.*, 
           CASE 
               WHEN t.type = 'deposit' THEN 'Deposit'
               WHEN t.type = 'withdrawal' THEN 'Withdraw'
               WHEN t.type = 'commission' THEN 'Commission'
               WHEN t.type = 'task_reward' THEN 'Task Reward'
               WHEN t.type = 'bonus' THEN 'Bonus'
               ELSE UPPER(t.type)
           END as display_type
    FROM transactions t 
    WHERE t.user_id = ? 
    ORDER BY t.created_at DESC 
    LIMIT 10
", [$user_id]);

// Page configuration
$page_title = 'Profile';
$page_description = 'User profile and account management';
$page_css = 'profile.css';
$page_js = 'profile.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Profile Header -->
    <div class="profile-header user-fade-in">
        <div class="profile-avatar-section">
            <div class="profile-avatar">
                <?php if (!empty($current_user['avatar_url'])): ?>
                    <img src="<?php echo htmlspecialchars($current_user['avatar_url']); ?>" alt="Profile Avatar" class="avatar-image">
                <?php else: ?>
                    <div class="avatar-placeholder">
                        <?php echo strtoupper(substr($current_user['username'], 0, 2)); ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="profile-info">
                <h1 class="profile-username"><?php echo htmlspecialchars($current_user['username']); ?></h1>
                <div class="profile-level">
                    <span class="vip-badge">VIP <?php echo $user_vip['level'] ?? 1; ?></span>
                    <span class="level-name"><?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?></span>
                </div>
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $user_stats['total_tasks']; ?></span>
                        <span class="stat-label">Tasks Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $user_stats['referral_count']; ?></span>
                        <span class="stat-label">Referrals</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="profile-balance-section">
            <div class="balance-card">
                <h3>Total Balance</h3>
                <div class="balance-amount">USDT <?php echo formatCurrency($user_balance['balance'] ?? 0); ?></div>
            </div>
            <div class="balance-card">
                <h3>Commission Balance</h3>
                <div class="balance-amount">USDT <?php echo formatCurrency($user_balance['commission_balance'] ?? 0); ?></div>
            </div>
        </div>
    </div>

    <!-- Profile Menu Grid -->
    <div class="profile-menu-grid user-fade-in">
        <!-- Transactions Section -->
        <div class="profile-menu-section">
            <h4 class="section-title">Transactions</h4>
            <div class="menu-items">
                <a href="<?php echo BASE_URL; ?>user/deposit/" class="profile-menu-item">
                    <div class="menu-icon deposit-icon">
                        <i class="icon-deposit"></i>
                    </div>
                    <span class="menu-text">Deposit</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/salary/" class="profile-menu-item">
                    <div class="menu-icon salary-icon">
                        <i class="icon-salary"></i>
                    </div>
                    <span class="menu-text">Salary</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/withdraw/" class="profile-menu-item">
                    <div class="menu-icon withdraw-icon">
                        <i class="icon-withdraw"></i>
                    </div>
                    <span class="menu-text">Withdraw</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/records/" class="profile-menu-item">
                    <div class="menu-icon records-icon">
                        <i class="icon-records"></i>
                    </div>
                    <span class="menu-text">Records</span>
                </a>
            </div>
        </div>

        <!-- Account Management Section -->
        <div class="profile-menu-section">
            <h4 class="section-title">Account Management</h4>
            <div class="menu-items">
                <a href="<?php echo BASE_URL; ?>user/profile/edit.php" class="profile-menu-item">
                    <div class="menu-icon edit-icon">
                        <i class="icon-edit"></i>
                    </div>
                    <span class="menu-text">Personal Information</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/profile/withdrawal-info.php" class="profile-menu-item">
                    <div class="menu-icon withdrawal-info-icon">
                        <i class="icon-bank"></i>
                    </div>
                    <span class="menu-text">Withdrawal Information</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/contact/" class="profile-menu-item">
                    <div class="menu-icon contact-icon">
                        <i class="icon-contact"></i>
                    </div>
                    <span class="menu-text">Contact Us</span>
                </a>
                
                <a href="<?php echo BASE_URL; ?>user/notifications/" class="profile-menu-item">
                    <div class="menu-icon notifications-icon">
                        <i class="icon-notifications"></i>
                    </div>
                    <span class="menu-text">Notifications</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="user-card recent-transactions-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Recent Transactions</h5>
            <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-btn user-btn-sm user-btn-outline">View All</a>
        </div>
        <div class="user-card-body">
            <?php if (!empty($recent_transactions)): ?>
                <div class="transaction-table">
                    <?php foreach ($recent_transactions as $transaction): ?>
                        <div class="transaction-row">
                            <div class="transaction-type">
                                <div class="type-icon">
                                    <i class="icon-<?php echo $transaction['type']; ?>"></i>
                                </div>
                                <div class="type-info">
                                    <span class="type-name"><?php echo $transaction['display_type']; ?></span>
                                    <span class="type-date"><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></span>
                                </div>
                            </div>
                            <div class="transaction-amount <?php echo ($transaction['amount'] > 0) ? 'positive' : 'negative'; ?>">
                                <?php echo ($transaction['amount'] > 0) ? '+' : ''; ?><?php echo formatCurrency($transaction['amount']); ?>
                            </div>
                            <div class="transaction-status">
                                <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                    <?php echo ucfirst($transaction['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-transactions">
                    <div class="no-data-icon">
                        <i class="icon-transactions"></i>
                    </div>
                    <p>No transactions found</p>
                    <a href="<?php echo BASE_URL; ?>user/deposit/" class="user-btn user-btn-primary">Make Your First Deposit</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Logout Section -->
    <div class="profile-logout-section user-fade-in">
        <a href="<?php echo BASE_URL; ?>user/logout/" class="logout-btn" onclick="return confirm('Are you sure you want to logout?')">
            <i class="icon-logout"></i>
            <span>Logout</span>
        </a>
    </div>
</div>

<?php
// Include footer
include '../includes/user_footer.php';
?>
