/**
 * Bamboo User Dashboard - Dashboard Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Interactive functionality for the main dashboard page
 */

// Dashboard-specific functionality
const Dashboard = {
    config: {
        refreshInterval: 5 * 60 * 1000, // 5 minutes
        animationDelay: 100,
        chartColors: {
            primary: '#ff6900',
            success: '#28a745',
            info: '#17a2b8',
            warning: '#ffc107'
        }
    },

    init: function() {
        this.bindEvents();
        this.initializeAnimations();
        this.loadDashboardData();
        this.setupAutoRefresh();
        this.initializeCharts();
        console.log('Dashboard initialized');
    },

    bindEvents: function() {
        // Refresh balance button
        $('.refresh-balance').on('click', this.refreshBalance.bind(this));
        
        // Quick action buttons
        $('.quick-action-btn').on('click', this.handleQuickAction.bind(this));
        
        // Task item clicks
        $('.task-item').on('click', this.handleTaskClick.bind(this));
        
        // Transaction item clicks
        $('.transaction-item').on('click', this.handleTransactionClick.bind(this));
        
        // VIP progress click
        $('.vip-current').on('click', this.handleVipClick.bind(this));
        
        // Notification clicks
        $('.notification-item').on('click', this.handleNotificationClick.bind(this));
    },

    initializeAnimations: function() {
        // Stagger animation for balance cards
        $('.balance-card').each(function(index) {
            $(this).css('animation-delay', (index * Dashboard.config.animationDelay) + 'ms');
        });

        // Add hover effects to interactive elements
        $('.task-item, .transaction-item, .quick-action-btn').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );
    },

    loadDashboardData: function() {
        // Load real-time dashboard data
        this.loadBalanceData();
        this.loadTaskData();
        this.loadTransactionData();
        this.loadNotifications();
    },

    loadBalanceData: function() {
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/balance.php',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Dashboard.updateBalanceDisplay(response.data);
                }
            },
            error: function() {
                console.log('Failed to load balance data');
            }
        });
    },

    loadTaskData: function() {
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/tasks.php',
            method: 'GET',
            data: { action: 'dashboard_summary' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Dashboard.updateTaskDisplay(response.data);
                }
            },
            error: function() {
                console.log('Failed to load task data');
            }
        });
    },

    loadTransactionData: function() {
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/transactions.php',
            method: 'GET',
            data: { action: 'recent', limit: 5 },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Dashboard.updateTransactionDisplay(response.data);
                }
            },
            error: function() {
                console.log('Failed to load transaction data');
            }
        });
    },

    loadNotifications: function() {
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/notifications.php',
            method: 'GET',
            data: { action: 'recent', limit: 3 },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Dashboard.updateNotificationDisplay(response.data);
                }
            },
            error: function() {
                console.log('Failed to load notifications');
            }
        });
    },

    updateBalanceDisplay: function(data) {
        // Update balance amounts with animation
        $('.user-balance').each(function() {
            const $element = $(this);
            const newValue = data.balance_formatted;
            if ($element.text() !== newValue) {
                $element.fadeOut(200, function() {
                    $element.text(newValue).fadeIn(200);
                });
            }
        });

        $('.user-commission-balance').each(function() {
            const $element = $(this);
            const newValue = data.commission_balance_formatted;
            if ($element.text() !== newValue) {
                $element.fadeOut(200, function() {
                    $element.text(newValue).fadeIn(200);
                });
            }
        });

        // Update UserApp config
        UserApp.config.balance = data.balance;
        UserApp.config.commissionBalance = data.commission_balance;
    },

    updateTaskDisplay: function(data) {
        // Update task statistics
        $('.task-stats .stat-number').each(function(index) {
            const $element = $(this);
            const values = [
                data.tasks_today,
                data.total_completed,
                data.active_tasks,
                data.daily_limit
            ];
            
            if (values[index] !== undefined) {
                Dashboard.animateNumber($element, values[index]);
            }
        });

        // Update active tasks list if needed
        if (data.active_tasks_list) {
            Dashboard.updateActiveTasksList(data.active_tasks_list);
        }
    },

    updateTransactionDisplay: function(data) {
        // Update recent transactions list
        const $transactionList = $('.transaction-list');
        if (data.length > 0) {
            let html = '';
            data.forEach(function(transaction) {
                html += Dashboard.buildTransactionItem(transaction);
            });
            $transactionList.html(html);
        }
    },

    updateNotificationDisplay: function(data) {
        // Update notifications list
        const $notificationList = $('.notification-list');
        if (data.length > 0) {
            let html = '';
            data.forEach(function(notification) {
                html += Dashboard.buildNotificationItem(notification);
            });
            $notificationList.html(html);
        }
    },

    buildTransactionItem: function(transaction) {
        const amountClass = transaction.amount > 0 ? 'positive' : 'negative';
        const amountPrefix = transaction.amount > 0 ? '+' : '';
        
        return `
            <div class="transaction-item" data-id="${transaction.id}">
                <div class="transaction-icon">
                    <i class="icon-${transaction.type}"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-type">${transaction.type_formatted}</div>
                    <div class="transaction-date">${transaction.date_formatted}</div>
                </div>
                <div class="transaction-amount ${amountClass}">
                    ${amountPrefix}${transaction.amount_formatted}
                </div>
            </div>
        `;
    },

    buildNotificationItem: function(notification) {
        return `
            <div class="notification-item" data-id="${notification.id}">
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${notification.time_ago}</div>
                </div>
            </div>
        `;
    },

    animateNumber: function($element, newValue) {
        const currentValue = parseInt($element.text()) || 0;
        if (currentValue !== newValue) {
            $({ value: currentValue }).animate({ value: newValue }, {
                duration: 1000,
                step: function() {
                    $element.text(Math.floor(this.value));
                },
                complete: function() {
                    $element.text(newValue);
                }
            });
        }
    },

    refreshBalance: function(e) {
        e.preventDefault();
        const $button = $(e.currentTarget);
        
        // Add loading state
        $button.addClass('user-loading').prop('disabled', true);
        
        $.ajax({
            url: UserApp.config.baseUrl + 'user/api/balance.php',
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    Dashboard.updateBalanceDisplay(response.data);
                    UserApp.showNotification('Balance updated successfully', 'success', 2000);
                } else {
                    UserApp.showNotification('Failed to update balance', 'error');
                }
            },
            error: function() {
                UserApp.showNotification('Network error occurred', 'error');
            },
            complete: function() {
                $button.removeClass('user-loading').prop('disabled', false);
            }
        });
    },

    handleQuickAction: function(e) {
        e.preventDefault();
        const $button = $(e.currentTarget);
        const href = $button.attr('href');
        
        // Add click animation
        $button.addClass('clicked');
        setTimeout(() => {
            $button.removeClass('clicked');
            if (href) {
                window.location.href = href;
            }
        }, 150);
    },

    handleTaskClick: function(e) {
        const $item = $(e.currentTarget);
        const taskId = $item.data('id');
        
        if (taskId) {
            window.location.href = UserApp.config.baseUrl + 'user/tasks/?task=' + taskId;
        }
    },

    handleTransactionClick: function(e) {
        const $item = $(e.currentTarget);
        const transactionId = $item.data('id');
        
        if (transactionId) {
            window.location.href = UserApp.config.baseUrl + 'user/transactions/?transaction=' + transactionId;
        }
    },

    handleVipClick: function(e) {
        e.preventDefault();
        window.location.href = UserApp.config.baseUrl + 'user/vip/';
    },

    handleNotificationClick: function(e) {
        const $item = $(e.currentTarget);
        const notificationId = $item.data('id');
        
        // Mark as read
        $.post(UserApp.config.baseUrl + 'user/api/notifications.php', {
            action: 'mark_read',
            id: notificationId,
            csrf_token: UserApp.config.csrfToken
        });
        
        $item.addClass('read');
    },

    setupAutoRefresh: function() {
        // Auto-refresh dashboard data every 5 minutes
        setInterval(() => {
            this.loadDashboardData();
        }, this.config.refreshInterval);
    },

    initializeCharts: function() {
        // Initialize any charts or graphs
        // This can be expanded with Chart.js or other charting libraries
        this.initializeVipProgress();
    },

    initializeVipProgress: function() {
        // Animate VIP progress bar
        const $progressBar = $('.progress-bar');
        if ($progressBar.length) {
            const width = $progressBar.css('width');
            $progressBar.css('width', '0%').animate({ width: width }, 1000);
        }
    }
};



// USDT Banner functions
function closeUsdtBanner() {
    const banner = document.getElementById('usdtNotificationBanner');
    if (banner) {
        banner.style.animation = 'user-slideOut 0.3s ease-out';
        setTimeout(() => {
            banner.style.display = 'none';
        }, 300);

        // Store in localStorage to remember user closed it
        localStorage.setItem('usdt_banner_closed_' + new Date().toDateString(), 'true');
    }
}

// Banner functions
function closeBanner() {
    const banner = document.getElementById('notificationBanner');
    if (banner) {
        banner.style.animation = 'user-slideOut 0.3s ease-out';
        setTimeout(() => {
            banner.style.display = 'none';
        }, 300);

        // Store in localStorage to remember user closed it
        localStorage.setItem('banner_closed_' + new Date().toDateString(), 'true');
    }
}

// Check if banners should be shown
function checkBannerVisibility() {
    const today = new Date().toDateString();

    // Check USDT banner
    const usdtBanner = document.getElementById('usdtNotificationBanner');
    const usdtBannerClosed = localStorage.getItem('usdt_banner_closed_' + today);
    if (usdtBannerClosed === 'true' && usdtBanner) {
        usdtBanner.style.display = 'none';
    }

    // Check notification banner
    const banner = document.getElementById('notificationBanner');
    const bannerClosed = localStorage.getItem('banner_closed_' + today);
    if (bannerClosed === 'true' && banner) {
        banner.style.display = 'none';
    }
}

// USDT Modal Functions
function showUsdtModal() {
    const modal = document.getElementById('usdtNotificationModal');
    if (modal && typeof bootstrap !== 'undefined') {
        const usdtModal = new bootstrap.Modal(modal);
        usdtModal.show();
    }
}

function hideUsdtModal() {
    const modal = document.getElementById('usdtNotificationModal');
    if (modal && typeof bootstrap !== 'undefined') {
        const usdtModal = bootstrap.Modal.getInstance(modal);
        if (usdtModal) {
            usdtModal.hide();
        }
    }
}

// Initialize dashboard when page is ready
function initializePage() {
    Dashboard.init();
    checkBannerVisibility();

    // Show USDT modal after 3 seconds for better user experience
    setTimeout(() => {
        showUsdtModal();
    }, 3000);
}

// Export for global access
window.Dashboard = Dashboard;
window.closeUsdtBanner = closeUsdtBanner;
window.closeBanner = closeBanner;
window.showUsdtModal = showUsdtModal;
window.hideUsdtModal = hideUsdtModal;
