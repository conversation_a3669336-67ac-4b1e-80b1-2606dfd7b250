<?php
/**
 * Test database and check for sample users
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

echo "<h1>Database Test</h1>";

try {
    $db = getDB();
    echo "<h2>Database Connection: SUCCESS</h2>";
    
    // Check users table
    echo "<h3>Users in database:</h3>";
    $users = fetchAll("SELECT id, username, phone, email, balance, vip_level, status FROM users LIMIT 10");
    if ($users) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Username</th><th>Phone</th><th>Email</th><th>Balance</th><th>VIP Level</th><th>Status</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['phone'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['balance'] . "</td>";
            echo "<td>" . $user['vip_level'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No users found in database.";
        echo "<br><br><strong>You need to insert sample data first!</strong>";
        echo "<br>Run this SQL: <br><textarea rows='10' cols='80'>";
        echo file_get_contents('../sql/insert_sample_data.sql');
        echo "</textarea>";
    }
    
    // Check VIP levels
    echo "<h3>VIP Levels:</h3>";
    $vip_levels = fetchAll("SELECT * FROM vip_levels ORDER BY level");
    if ($vip_levels) {
        echo "<table border='1'>";
        echo "<tr><th>Level</th><th>Name</th><th>Min Balance</th><th>Max Daily Tasks</th><th>Commission Rate</th></tr>";
        foreach ($vip_levels as $vip) {
            echo "<tr>";
            echo "<td>" . $vip['level'] . "</td>";
            echo "<td>" . $vip['name'] . "</td>";
            echo "<td>" . $vip['min_balance'] . "</td>";
            echo "<td>" . $vip['max_daily_tasks'] . "</td>";
            echo "<td>" . $vip['commission_rate'] . "%</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No VIP levels found.";
    }
    
    // Test password verification
    echo "<h3>Password Test:</h3>";
    $test_password = 'password';
    $test_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
    
    if (password_verify($test_password, $test_hash)) {
        echo "Password 'password' matches the hash: <strong>YES</strong>";
    } else {
        echo "Password 'password' matches the hash: <strong>NO</strong>";
        echo "<br>Creating new hash for 'password': " . password_hash($test_password, PASSWORD_DEFAULT);
    }
    
} catch (Exception $e) {
    echo "<h2>Database Connection: FAILED</h2>";
    echo "Error: " . $e->getMessage();
}
?>
