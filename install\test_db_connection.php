<?php
/**
 * Simple Database Connection Test
 * Use this to test your database credentials
 */

header('Content-Type: application/json');

// Get POST data
$host = $_POST['db_host'] ?? 'localhost';
$name = $_POST['db_name'] ?? '';
$user = $_POST['db_user'] ?? '';
$pass = $_POST['db_pass'] ?? '';
$port = $_POST['db_port'] ?? '3306';

try {
    // Validate input
    if (empty($host) || empty($name) || empty($user)) {
        throw new Exception('Missing required database credentials');
    }

    // Create connection
    $dsn = "mysql:host=$host;port=$port;dbname=$name;charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 10
    ]);

    // Test query
    $result = $pdo->query("SELECT 1 as test, NOW() as current_time");
    $row = $result->fetch();
    
    if ($row['test'] == 1) {
        echo json_encode([
            'success' => true, 
            'message' => 'Database connection successful!',
            'server_time' => $row['current_time'],
            'details' => "Connected to $name on $host:$port"
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Database query test failed']);
    }

} catch (PDOException $e) {
    echo json_encode([
        'success' => false, 
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => 'Error: ' . $e->getMessage()
    ]);
}
?>
