<?php
/**
 * Test session and user authentication
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Session Test</h1>";
echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>isLoggedIn():</h2>";
echo isLoggedIn() ? "TRUE" : "FALSE";

echo "<h2>getCurrentUserId():</h2>";
echo getCurrentUserId() ?? "NULL";

if (isLoggedIn()) {
    echo "<h2>getCurrentUser():</h2>";
    echo "<pre>";
    print_r(getCurrentUser());
    echo "</pre>";
}

echo "<h2>Database Connection Test:</h2>";
try {
    $db = getDB();
    echo "Database connection: SUCCESS";
    
    // Test a simple query
    $result = fetchRow("SELECT COUNT(*) as count FROM users");
    echo "<br>Users count: " . $result['count'];
} catch (Exception $e) {
    echo "Database connection: FAILED - " . $e->getMessage();
}
?>
