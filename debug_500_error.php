<?php
/**
 * Debug 500 Error in Start Matching
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Debug 500 Error - Start Matching</h1>";

if (!isLoggedIn()) {
    echo "<p>❌ Please login first: <a href='user/login/login.php'>Login</a></p>";
    exit;
}

$user_id = getCurrentUserId();
echo "<p>✅ User logged in: ID $user_id</p>";

// Test 1: Check if we can access the API file directly
echo "<h2>Test 1: API File Access</h2>";
if (file_exists('user/api/tasks.php')) {
    echo "✅ API file exists<br>";
} else {
    echo "❌ API file missing<br>";
}

// Test 2: Check database connection
echo "<h2>Test 2: Database Connection</h2>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Check required functions
echo "<h2>Test 3: Required Functions</h2>";
$required_functions = [
    'getCurrentUser', 'getUserBalance', 'getUserVipLevel', 
    'getTasksCompletedToday', 'getAppSetting', 'fetchRow', 
    'executeQuery', 'recordTransaction', 'updateRecord'
];

foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "✅ Function $func exists<br>";
    } else {
        echo "❌ Function $func missing<br>";
    }
}

// Test 4: Check user data
echo "<h2>Test 4: User Data</h2>";
try {
    $user = getCurrentUser();
    echo "✅ Current user: " . ($user['username'] ?? 'Unknown') . "<br>";
    
    $balance = getUserBalance($user_id);
    echo "✅ User balance: " . ($balance['balance'] ?? 'Unknown') . "<br>";
    
    $vip = getUserVipLevel($user_id);
    echo "✅ VIP level: " . ($vip['level'] ?? 'Unknown') . "<br>";
    
    $tasks_today = getTasksCompletedToday($user_id);
    echo "✅ Tasks today: $tasks_today<br>";
    
} catch (Exception $e) {
    echo "❌ User data error: " . $e->getMessage() . "<br>";
}

// Test 5: Simulate the exact API call
echo "<h2>Test 5: Simulate API Call</h2>";
try {
    // Set up the environment exactly like the AJAX call
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST['action'] = 'start_matching';
    $_POST['csrf_token'] = generateCSRFToken();
    
    echo "✅ POST data set up<br>";
    echo "CSRF Token: " . $_POST['csrf_token'] . "<br>";
    
    // Capture any output from the API
    ob_start();
    
    // Include the API file
    include 'user/api/tasks.php';
    
    $api_output = ob_get_clean();
    
    echo "<h3>API Output:</h3>";
    echo "<pre>" . htmlspecialchars($api_output) . "</pre>";
    
} catch (Exception $e) {
    echo "❌ API simulation failed: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test 6: Check error logs
echo "<h2>Test 6: Recent Error Logs</h2>";
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    $recent_errors = array_slice(file($error_log_file), -10);
    echo "<pre>" . implode('', $recent_errors) . "</pre>";
} else {
    echo "No error log file found or accessible<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>Check the output above to identify the 500 error cause.</p>";
?>
