/**
 * Bamboo User Application - Profile Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize profile page
    initializeProfile();
    
    // Add smooth animations
    addProfileAnimations();
    
    // Handle profile interactions
    handleProfileInteractions();
});

function initializeProfile() {
    // Add loading states for dynamic content
    const balanceCards = document.querySelectorAll('.balance-card');
    balanceCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add refresh animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // Initialize transaction table interactions
    const transactionRows = document.querySelectorAll('.transaction-row');
    transactionRows.forEach(row => {
        row.addEventListener('click', function() {
            // Add click feedback
            this.style.background = '#e0f2fe';
            setTimeout(() => {
                this.style.background = '';
            }, 200);
        });
    });
}

function addProfileAnimations() {
    // Stagger animation for menu items
    const menuItems = document.querySelectorAll('.profile-menu-item');
    menuItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('user-fade-in');
    });
    
    // Add hover effects for balance cards
    const balanceCards = document.querySelectorAll('.balance-card');
    balanceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(255, 255, 255, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

function handleProfileInteractions() {
    // Handle menu item clicks with loading states
    const menuItems = document.querySelectorAll('.profile-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('.menu-icon');
            const originalContent = icon.innerHTML;
            
            icon.innerHTML = '<i class="icon-loading"></i>';
            icon.style.animation = 'spin 1s linear infinite';
            
            // Restore after navigation (in case of same-page navigation)
            setTimeout(() => {
                icon.innerHTML = originalContent;
                icon.style.animation = '';
            }, 1000);
        });
    });
    
    // Handle logout confirmation
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Custom confirmation dialog
            if (confirm('Are you sure you want to logout?')) {
                // Add logout animation
                this.innerHTML = '<i class="icon-loading"></i> Logging out...';
                this.style.pointerEvents = 'none';
                
                // Redirect after animation
                setTimeout(() => {
                    window.location.href = this.href;
                }, 1000);
            }
        });
    }
}

// Profile utility functions
const ProfileUtils = {
    // Refresh balance display
    refreshBalance: function() {
        const balanceCards = document.querySelectorAll('.balance-amount');
        balanceCards.forEach(card => {
            card.style.opacity = '0.5';
            // Simulate API call
            setTimeout(() => {
                card.style.opacity = '1';
                UserApp.showNotification('Balance updated', 'success');
            }, 1000);
        });
    },
    
    // Update profile stats
    updateStats: function() {
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
            stat.style.transform = 'scale(1.1)';
            setTimeout(() => {
                stat.style.transform = 'scale(1)';
            }, 300);
        });
    },
    
    // Handle avatar upload (for future implementation)
    handleAvatarUpload: function(file) {
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const avatar = document.querySelector('.avatar-image, .avatar-placeholder');
                if (avatar) {
                    if (avatar.tagName === 'IMG') {
                        avatar.src = e.target.result;
                    } else {
                        // Replace placeholder with image
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'avatar-image';
                        img.alt = 'Profile Avatar';
                        avatar.parentNode.replaceChild(img, avatar);
                    }
                }
            };
            reader.readAsDataURL(file);
        }
    }
};

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .profile-menu-item {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .transaction-row {
        transition: all 0.3s ease;
    }
    
    .transaction-row:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .balance-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .menu-icon {
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover .menu-icon {
        transform: scale(1.1);
    }
`;
document.head.appendChild(style);

// Export for global access
window.ProfileUtils = ProfileUtils;
