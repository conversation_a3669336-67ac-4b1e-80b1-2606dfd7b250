<?php
/**
 * Bamboo User Dashboard - Transactions API
 * Company: Notepadsly
 * Version: 1.0
 * Description: API endpoint for user transaction information
 */

// Define app constant
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

// Include required files
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON content type
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Only allow GET requests for now
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    $user_id = getCurrentUserId();
    $action = $_GET['action'] ?? 'recent';
    
    switch ($action) {
        case 'recent':
            // Get recent transactions
            $limit = intval($_GET['limit'] ?? 5);
            $transactions = getRecentTransactions($user_id, $limit);
            
            // Format transactions for display
            $formatted_transactions = [];
            foreach ($transactions as $transaction) {
                $formatted_transactions[] = [
                    'id' => $transaction['id'],
                    'type' => $transaction['type'],
                    'type_formatted' => ucfirst(str_replace('_', ' ', $transaction['type'])),
                    'amount' => floatval($transaction['amount']),
                    'amount_formatted' => formatCurrency($transaction['amount']),
                    'status' => $transaction['status'],
                    'description' => $transaction['description'],
                    'created_at' => $transaction['created_at'],
                    'date_formatted' => date('M j, Y H:i', strtotime($transaction['created_at']))
                ];
            }
            
            $response = [
                'success' => true,
                'data' => $formatted_transactions
            ];
            break;
            
        case 'history':
            // Get transaction history with pagination
            $limit = intval($_GET['limit'] ?? 20);
            $offset = intval($_GET['offset'] ?? 0);
            $type = $_GET['type'] ?? null;
            
            $sql = "SELECT * FROM transactions WHERE user_id = ?";
            $params = [$user_id];
            
            if ($type) {
                $sql .= " AND type = ?";
                $params[] = $type;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $transactions = fetchAll($sql, $params);
            
            // Format transactions
            $formatted_transactions = [];
            foreach ($transactions as $transaction) {
                $formatted_transactions[] = [
                    'id' => $transaction['id'],
                    'type' => $transaction['type'],
                    'type_formatted' => ucfirst(str_replace('_', ' ', $transaction['type'])),
                    'amount' => floatval($transaction['amount']),
                    'amount_formatted' => formatCurrency($transaction['amount']),
                    'status' => $transaction['status'],
                    'description' => $transaction['description'],
                    'created_at' => $transaction['created_at'],
                    'date_formatted' => date('M j, Y H:i', strtotime($transaction['created_at'])),
                    'balance_before' => floatval($transaction['balance_before']),
                    'balance_after' => floatval($transaction['balance_after'])
                ];
            }
            
            $response = [
                'success' => true,
                'data' => $formatted_transactions
            ];
            break;
            
        case 'summary':
            // Get transaction summary
            $sql = "SELECT 
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                        SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals,
                        SUM(CASE WHEN type = 'commission' THEN amount ELSE 0 END) as total_commissions
                    FROM transactions 
                    WHERE user_id = ?";
            
            $summary = fetchRow($sql, [$user_id]);
            
            $response = [
                'success' => true,
                'data' => [
                    'total_transactions' => intval($summary['total_transactions']),
                    'total_deposits' => floatval($summary['total_deposits']),
                    'total_withdrawals' => floatval($summary['total_withdrawals']),
                    'total_commissions' => floatval($summary['total_commissions']),
                    'total_deposits_formatted' => formatCurrency($summary['total_deposits']),
                    'total_withdrawals_formatted' => formatCurrency($summary['total_withdrawals']),
                    'total_commissions_formatted' => formatCurrency($summary['total_commissions'])
                ]
            ];
            break;
            
        default:
            throw new Exception('Invalid action specified');
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log error
    logError('Transactions API error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to retrieve transaction information'
    ]);
}
?>
