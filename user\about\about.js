/**
 * Bamboo User Dashboard - About Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: About page functionality
 */

// About page management object
const AboutManager = {
    init: function() {
        this.initializeAnimations();
        this.animateStats();
        console.log('AboutManager initialized');
    },

    initializeAnimations: function() {
        // Animate company logo
        $('.logo-circle').css({
            'transform': 'scale(0) rotate(-180deg)',
            'opacity': '0'
        }).animate({
            'opacity': '1'
        }, 800).css({
            'transform': 'scale(1) rotate(0deg)',
            'transition': 'transform 0.8s ease-out'
        });

        // Stagger animation for feature items
        $('.feature-item').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(30px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 600).css('transform', 'translateY(0)');
        });

        // Animate mission and vision icons
        $('.mission-icon, .vision-icon').css({
            'transform': 'scale(0)',
            'opacity': '0'
        }).delay(500).animate({
            'opacity': '1'
        }, 600).css({
            'transform': 'scale(1)',
            'transition': 'transform 0.6s ease-out'
        });
    },

    animateStats: function() {
        // Animate statistics numbers
        $('.stat-number').each(function() {
            const $this = $(this);
            const text = $this.text();
            
            // Extract number from text (handle K, M suffixes and percentages)
            let finalValue = 0;
            let suffix = '';
            let isPercentage = false;
            
            if (text.includes('%')) {
                finalValue = parseFloat(text.replace('%', ''));
                suffix = '%';
                isPercentage = true;
            } else if (text.includes('K+')) {
                finalValue = parseFloat(text.replace('K+', '')) * 1000;
                suffix = 'K+';
            } else if (text.includes('M+')) {
                finalValue = parseFloat(text.replace('M+', '')) * 1000000;
                suffix = 'M+';
            } else if (text.includes('$')) {
                finalValue = parseFloat(text.replace(/[$K+]/g, ''));
                if (text.includes('K+')) {
                    finalValue *= 1000;
                    suffix = 'K+';
                }
                suffix = '$' + suffix;
            } else {
                finalValue = parseFloat(text.replace(/[^\d.]/g, ''));
            }
            
            if (finalValue > 0) {
                $this.text(isPercentage ? '0%' : (suffix.includes('$') ? '$0' : '0'));
                
                $({ value: 0 }).animate({ value: finalValue }, {
                    duration: 2000,
                    step: function() {
                        let displayValue = Math.floor(this.value);
                        
                        if (isPercentage) {
                            $this.text(displayValue + '%');
                        } else if (suffix.includes('K+')) {
                            if (suffix.includes('$')) {
                                $this.text('$' + Math.floor(this.value / 1000) + 'K+');
                            } else {
                                $this.text(Math.floor(this.value / 1000) + 'K+');
                            }
                        } else if (suffix.includes('M+')) {
                            $this.text(Math.floor(this.value / 1000000) + 'M+');
                        } else {
                            $this.text(displayValue + suffix);
                        }
                    },
                    complete: function() {
                        $this.text(text); // Restore original text
                    }
                });
            }
        });
    }
};

// Initialize when page is ready
function initializePage() {
    AboutManager.init();
}

// Export for global access
window.AboutManager = AboutManager;
