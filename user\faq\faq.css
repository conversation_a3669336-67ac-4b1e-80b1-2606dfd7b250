/**
 * Bamboo User Dashboard - FAQ Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: FAQ page styling
 */

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
    padding: var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius-lg);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin: 0 0 var(--user-spacing-sm) 0;
}

.page-subtitle {
    color: var(--user-text-secondary);
    margin: 0;
    font-size: var(--user-font-size-lg);
}

/* ===== SEARCH SECTION ===== */
.search-container {
    max-width: 500px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    gap: var(--user-spacing-sm);
}

.search-input-group .user-form-control {
    flex: 1;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border: 1px solid rgba(0, 0, 0, 0.08);
    padding: var(--user-spacing) var(--user-spacing-lg);
    font-size: var(--user-font-size);
}

.search-btn {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border: none;
    padding: var(--user-spacing) var(--user-spacing-lg);
    border-radius: var(--user-border-radius);
    cursor: pointer;
    transition: var(--user-transition);
    min-width: 60px;
}

.search-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--user-shadow);
}

/* ===== FAQ CATEGORIES ===== */
.faq-container {
    display: grid;
    gap: var(--user-spacing-xl);
}

.faq-category {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius-lg);
    overflow: hidden;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg) var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.category-title {
    font-size: var(--user-font-size-xl);
    font-weight: 600;
    color: var(--user-primary);
    margin: 0;
}

.question-count {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
    font-weight: 500;
}

/* ===== FAQ ITEMS ===== */
.faq-items {
    padding: 0;
}

.faq-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    transition: var(--user-transition);
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-item.search-highlight {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    border-left: 4px solid var(--user-warning);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg) var(--user-spacing-xl);
    cursor: pointer;
    transition: var(--user-transition);
    background: transparent;
}

.faq-question:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.95));
}

.faq-item.active .faq-question {
    background: linear-gradient(135deg, rgba(255, 105, 0, 0.1), rgba(255, 105, 0, 0.05));
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.question-text {
    font-weight: 600;
    color: var(--user-text-primary);
    font-size: var(--user-font-size);
    line-height: 1.4;
    flex: 1;
    margin-right: var(--user-spacing);
}

.toggle-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    border-radius: 50%;
    flex-shrink: 0;
}

.toggle-icon i {
    transition: transform 0.3s ease;
    font-size: var(--user-font-size-sm);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.answer-content {
    padding: 0 var(--user-spacing-xl) var(--user-spacing-lg) var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.3));
}

.answer-content p {
    color: var(--user-text-secondary);
    line-height: 1.6;
    margin: 0;
    padding-top: var(--user-spacing);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* ===== SUPPORT SECTION ===== */
.support-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border-radius: var(--user-border-radius);
}

.support-info h5 {
    margin: 0 0 var(--user-spacing-xs) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.support-info p {
    margin: 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
}

.support-actions {
    display: flex;
    gap: var(--user-spacing);
}

/* ===== ANIMATIONS ===== */
@keyframes highlightPulse {
    0%, 100% { background-color: rgba(255, 193, 7, 0.1); }
    50% { background-color: rgba(255, 193, 7, 0.2); }
}

.faq-item.search-highlight {
    animation: highlightPulse 2s ease-in-out infinite;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .category-header {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-sm);
    }
    
    .support-content {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-lg);
    }
    
    .support-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .faq-question {
        padding: var(--user-spacing) var(--user-spacing-lg);
    }
    
    .answer-content {
        padding: 0 var(--user-spacing-lg) var(--user-spacing) var(--user-spacing-lg);
    }
}

@media (max-width: 480px) {
    .search-input-group {
        flex-direction: column;
    }
    
    .support-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .question-text {
        font-size: var(--user-font-size-sm);
    }
}
