/**
 * Bamboo User Dashboard - Certificate Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: Certificate page styling
 */

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
    padding: var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius-lg);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin: 0 0 var(--user-spacing-sm) 0;
}

.page-subtitle {
    color: var(--user-text-secondary);
    margin: 0;
    font-size: var(--user-font-size-lg);
}

/* ===== CERTIFICATE PDF PREVIEW ===== */
.pdf-preview-container {
    width: 100%;
    height: 800px;
    border-radius: var(--user-border-radius);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
}

.certificate-pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: var(--user-border-radius);
}

.no-certificate {
    text-align: center;
    padding: var(--user-spacing-xxl);
}

.no-cert-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto var(--user-spacing-lg) auto;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--user-text-muted);
}

.no-certificate h4 {
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing);
    font-size: var(--user-font-size-xl);
}

.no-certificate p {
    color: var(--user-text-secondary);
    font-size: var(--user-font-size);
    line-height: 1.6;
}

/* ===== SECURITY FEATURES ===== */
.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--user-spacing-lg);
}

.security-item {
    display: flex;
    align-items: flex-start;
    gap: var(--user-spacing);
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
}

.security-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
}

.security-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.security-icon.ssl {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
}

.security-icon.secure {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.security-icon.verified {
    background: linear-gradient(135deg, var(--user-info), #17a2b8);
}

.security-icon.privacy {
    background: linear-gradient(135deg, var(--user-warning), #ffc107);
}

.security-content h6 {
    margin: 0 0 var(--user-spacing-xs) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.security-content p {
    margin: 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
    line-height: 1.5;
}



/* ===== DOWNLOAD SECTION ===== */
.download-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--user-spacing-lg);
}

.download-info h5 {
    margin: 0 0 var(--user-spacing-xs) 0;
    color: var(--user-text-primary);
}

.download-info p {
    margin: 0;
    color: var(--user-text-secondary);
}

.download-actions {
    display: flex;
    gap: var(--user-spacing);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .certificate-details {
        grid-template-columns: 1fr;
    }
    
    .security-grid {
        grid-template-columns: 1fr;
    }
    
    .download-content {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-lg);
    }
    
    .download-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .security-item {
        flex-direction: column;
        text-align: center;
    }
    
    .detail-item {
        flex-direction: column;
        text-align: center;
        gap: var(--user-spacing-xs);
    }
}
