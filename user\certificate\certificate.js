/**
 * Bamboo User Dashboard - Certificate Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: Certificate page functionality
 */

// Certificate management object
const CertificateManager = {
    init: function() {
        this.initializeAnimations();
        console.log('CertificateManager initialized');
    },

    initializeAnimations: function() {
        // Animate certificate badge
        $('.badge-icon').css({
            'transform': 'scale(0)',
            'opacity': '0'
        }).animate({
            'opacity': '1'
        }, 500).css({
            'transform': 'scale(1)',
            'transition': 'transform 0.5s ease-out'
        });

        // Stagger animation for security items
        $('.security-item').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            }).delay(index * 150).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateY(0)');
        });

        // Animate compliance items
        $('.compliance-item').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateX(-20px)'
            }).delay(index * 200).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateX(0)');
        });
    }
};

// Initialize when page is ready
function initializePage() {
    CertificateManager.init();
}

// Export for global access
window.CertificateManager = CertificateManager;
