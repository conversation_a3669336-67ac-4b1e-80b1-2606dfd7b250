-- Add diverse products for task matching system
-- This script adds 12 different products across various categories

INSERT INTO `products` (`name`, `description`, `image_url`, `price`, `commission_rate`, `category_id`, `min_vip_level`, `max_daily_assignments`, `weight`, `stock`, `status`) VALUES

-- Electronics Category (category_id = 1)
('iPhone 15 Pro Max', 'Latest Apple smartphone with titanium design and advanced camera system', '/uploads/products/iphone15-pro-max.jpg', 1199.00, 8.50, 1, 1, 50, 1, 100, 'active'),

('Samsung Galaxy S24 Ultra', 'Premium Android smartphone with S Pen and AI features', '/uploads/products/galaxy-s24-ultra.jpg', 1099.00, 8.00, 1, 1, 50, 1, 100, 'active'),

('MacBook Air M3', 'Ultra-thin laptop with M3 chip for everyday computing', '/uploads/products/macbook-air-m3.jpg', 1299.00, 10.00, 1, 2, 30, 1, 50, 'active'),

('Sony WH-1000XM5', 'Industry-leading noise canceling wireless headphones', '/uploads/products/sony-headphones.jpg', 399.00, 15.00, 1, 1, 80, 1, 150, 'active'),

-- Fashion & Accessories Category (category_id = 2)
('Nike Air Jordan 1', 'Classic basketball sneakers with premium leather construction', '/uploads/products/air-jordan-1.jpg', 170.00, 12.00, 2, 1, 100, 1, 200, 'active'),

('Rolex Submariner', 'Luxury Swiss diving watch with automatic movement', '/uploads/products/rolex-submariner.jpg', 8500.00, 5.00, 2, 4, 5, 1, 10, 'active'),

('Louis Vuitton Handbag', 'Designer leather handbag with iconic monogram pattern', '/uploads/products/lv-handbag.jpg', 2200.00, 6.00, 2, 3, 15, 1, 25, 'active'),

-- Home & Living Category (category_id = 3)
('Dyson V15 Detect', 'Cordless vacuum cleaner with laser dust detection', '/uploads/products/dyson-v15.jpg', 749.00, 10.00, 3, 2, 40, 1, 75, 'active'),

('KitchenAid Stand Mixer', 'Professional-grade stand mixer for baking enthusiasts', '/uploads/products/kitchenaid-mixer.jpg', 449.00, 12.00, 3, 1, 60, 1, 100, 'active'),

-- Sports & Fitness Category (category_id = 4)
('Peloton Bike+', 'Interactive exercise bike with live and on-demand classes', '/uploads/products/peloton-bike.jpg', 2495.00, 8.00, 4, 3, 10, 1, 20, 'active'),

('Apple Watch Series 9', 'Advanced smartwatch with health monitoring and fitness tracking', '/uploads/products/apple-watch-s9.jpg', 429.00, 11.00, 4, 1, 70, 1, 120, 'active'),

-- Beauty & Personal Care Category (category_id = 5)
('Dyson Airwrap', 'Multi-styler for hair styling without extreme heat damage', '/uploads/products/dyson-airwrap.jpg', 599.00, 13.00, 5, 2, 35, 1, 60, 'active');

-- Update existing products if they exist (optional)
UPDATE `products` SET 
    `description` = 'Latest iPhone with advanced features and titanium design',
    `commission_rate` = 8.00,
    `stock` = 100
WHERE `name` = 'iPhone 15 Pro' AND `id` <= 2;

UPDATE `products` SET 
    `description` = 'Premium Android smartphone with advanced camera system',
    `commission_rate` = 7.50,
    `stock` = 100
WHERE `name` = 'Samsung Galaxy S24' AND `id` <= 2;
