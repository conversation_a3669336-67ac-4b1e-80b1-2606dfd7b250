<?php
/**
 * Bamboo User Dashboard - Terms & Conditions
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Set page title
$page_title = 'Terms & Conditions';

// Get contract terms from admin settings
$contract_terms = getAppSetting('contract_terms', 'Terms and conditions content is being updated. Please check back later.');

// Include header
include '../includes/user_header.php';
?>

<div class="user-container-fluid">
    <div class="user-row">
        <div class="user-col-12">
            <!-- Page Header -->
            <div class="user-card user-fade-in">
                <div class="user-card-header">
                    <h1 class="user-card-title">
                        <i class="icon-document"></i>
                        Terms & Conditions
                    </h1>
                    <p class="user-card-subtitle">Platform terms, conditions, and user agreement</p>
                </div>
            </div>

            <!-- Terms Content -->
            <div class="user-card user-fade-in" style="animation-delay: 0.1s;">
                <div class="user-card-body">
                    <div class="terms-content">
                        <?php echo nl2br(htmlspecialchars($contract_terms)); ?>
                    </div>
                </div>
            </div>

            <!-- Back Button -->
            <div class="user-card user-fade-in" style="animation-delay: 0.2s;">
                <div class="user-card-body text-center">
                    <a href="<?php echo BASE_URL; ?>user/dashboard/" class="user-btn user-btn-primary">
                        <i class="icon-arrow-left"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.terms-content {
    line-height: 1.8;
    font-size: 1rem;
    color: var(--user-text-primary);
}

.terms-content h1, .terms-content h2, .terms-content h3 {
    color: var(--user-primary);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.terms-content h1:first-child {
    margin-top: 0;
}

.terms-content p {
    margin-bottom: 1rem;
}

.terms-content ul, .terms-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.terms-content li {
    margin-bottom: 0.5rem;
}

.icon-document::before {
    content: "📄";
    margin-right: 0.5rem;
}

.icon-arrow-left::before {
    content: "←";
    margin-right: 0.5rem;
}
</style>

<?php
// Include footer
include '../includes/user_footer.php';
?>
