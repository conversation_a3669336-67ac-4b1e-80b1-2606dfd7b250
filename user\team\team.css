/**
 * Bamboo User Dashboard - Team Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: Downline team management page styling
 */

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
    padding: var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius-lg);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin: 0 0 var(--user-spacing-sm) 0;
}

.page-subtitle {
    color: var(--user-text-secondary);
    margin: 0;
    font-size: var(--user-font-size-lg);
}

/* ===== STAT CARDS ===== */
.stat-card .user-card-body {
    display: flex;
    align-items: center;
    gap: var(--user-spacing);
    padding: var(--user-spacing-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.team-icon {
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
}

.active-icon {
    background: linear-gradient(135deg, var(--user-success), #20c997);
}

.commission-icon {
    background: linear-gradient(135deg, var(--user-warning), #ffc107);
}

.monthly-icon {
    background: linear-gradient(135deg, var(--user-info), #17a2b8);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: var(--user-font-size-xl);
    font-weight: 700;
    color: var(--user-text-primary);
    margin: 0 0 var(--user-spacing-xs) 0;
}

.stat-label {
    color: var(--user-text-muted);
    margin: 0;
    font-size: var(--user-font-size-sm);
}

/* ===== INVITATION SECTION ===== */
.invitation-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--user-spacing-xl);
}

.code-input-group {
    display: flex;
    gap: var(--user-spacing-sm);
    margin-top: var(--user-spacing-xs);
}

.code-input-group .user-form-control {
    flex: 1;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.code-input-group .user-btn {
    min-width: 80px;
}

/* ===== REFERRAL TABLE ===== */
.referral-table-container {
    overflow-x: auto;
    border-radius: var(--user-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.referral-table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
}

.referral-table thead {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
}

.referral-table th {
    padding: var(--user-spacing-lg);
    text-align: left;
    font-weight: 600;
    color: var(--user-text-primary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    font-size: var(--user-font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.referral-table td {
    padding: var(--user-spacing-lg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    color: var(--user-text-primary);
}

.referral-row:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.95));
}

.username-cell {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-sm);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--user-font-size-sm);
}

.vip-badge {
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
}

.vip-1 { background-color: #cd7f32; color: white; }
.vip-2 { background-color: #c0c0c0; color: white; }
.vip-3 { background-color: #ffd700; color: #333; }
.vip-4 { background-color: #e5e4e2; color: #333; }
.vip-5 { background-color: #b9f2ff; color: #333; }

.status-badge {
    padding: var(--user-spacing-xs) var(--user-spacing-sm);
    border-radius: var(--user-border-radius);
    font-size: var(--user-font-size-sm);
    font-weight: 500;
    text-transform: capitalize;
}

.status-active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--user-success);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--user-text-muted);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.commission-cell {
    font-weight: 600;
    color: var(--user-success);
}

/* ===== EMPTY STATE ===== */
.empty-state {
    text-align: center;
    padding: var(--user-spacing-xxl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--user-spacing-lg) auto;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--user-text-muted);
}

.empty-state h4 {
    color: var(--user-text-primary);
    margin-bottom: var(--user-spacing);
}

.empty-state p {
    color: var(--user-text-secondary);
    margin-bottom: var(--user-spacing-lg);
}

/* ===== COMMISSION RULES ===== */
.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--user-spacing-lg);
}

.rule-item {
    display: flex;
    align-items: flex-start;
    gap: var(--user-spacing);
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
}

.rule-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
}

.rule-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--user-border-radius);
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.rule-content h6 {
    margin: 0 0 var(--user-spacing-xs) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.rule-content p {
    margin: 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
    line-height: 1.5;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .invitation-content {
        grid-template-columns: 1fr;
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
    
    .referral-table-container {
        font-size: var(--user-font-size-sm);
    }
    
    .referral-table th,
    .referral-table td {
        padding: var(--user-spacing);
    }
}

@media (max-width: 480px) {
    .stat-card .user-card-body {
        flex-direction: column;
        text-align: center;
    }
    
    .code-input-group {
        flex-direction: column;
    }
}
