<?php
/**
 * Setup sample data for testing
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

echo "<h1>Sample Data Setup</h1>";

try {
    $db = getDB();
    
    // Check if users already exist
    $existing_users = fetchAll("SELECT COUNT(*) as count FROM users");
    $user_count = $existing_users[0]['count'] ?? 0;
    
    if ($user_count > 0) {
        echo "<p>Users already exist in database ($user_count users found).</p>";
    } else {
        echo "<p>No users found. Inserting sample data...</p>";
        
        // Create password hash for 'password'
        $password_hash = password_hash('password', PASSWORD_DEFAULT);
        $pin_hash = password_hash('1234', PASSWORD_DEFAULT);
        
        // Insert sample users
        $sql = "INSERT INTO users (username, phone, email, password_hash, withdrawal_pin_hash, gender, invitation_code, balance, commission_balance, frozen_balance, total_deposited, total_withdrawn, total_commission_earned, vip_level, tasks_completed_today, last_task_date, status, email_verified, phone_verified, referral_count, last_login, login_count, created_at, updated_at) VALUES 
        ('alice', '1234567890', '<EMAIL>', ?, ?, 'female', 'ALICE01', 1000.00, 100.00, 0.00, 2000.00, 500.00, 200.00, 2, 2, CURDATE(), 'active', 1, 1, 0, NOW(), 5, NOW(), NOW()),
        ('bob', '2345678901', '<EMAIL>', ?, ?, 'male', 'BOB01', 1500.00, 150.00, 0.00, 3000.00, 1000.00, 300.00, 3, 1, CURDATE(), 'active', 1, 1, 1, NOW(), 10, NOW(), NOW()),
        ('charlie', '3456789012', '<EMAIL>', ?, ?, 'male', 'CHARLIE01', 500.00, 50.00, 0.00, 1000.00, 200.00, 100.00, 1, 0, CURDATE(), 'active', 1, 1, 0, NOW(), 3, NOW(), NOW())";
        
        $result = executeQuery($sql, [
            $password_hash, $pin_hash,
            $password_hash, $pin_hash,
            $password_hash, $pin_hash
        ]);
        
        if ($result) {
            echo "<p><strong>Sample users created successfully!</strong></p>";
            echo "<ul>";
            echo "<li>Username: alice, Password: password, PIN: 1234</li>";
            echo "<li>Username: bob, Password: password, PIN: 1234</li>";
            echo "<li>Username: charlie, Password: password, PIN: 1234</li>";
            echo "</ul>";
        } else {
            echo "<p><strong>Failed to create sample users.</strong></p>";
        }
    }
    
    // Check VIP levels
    $vip_levels = fetchAll("SELECT COUNT(*) as count FROM vip_levels");
    $vip_count = $vip_levels[0]['count'] ?? 0;
    
    if ($vip_count == 0) {
        echo "<p>No VIP levels found. Creating default VIP levels...</p>";
        
        $vip_sql = "INSERT INTO vip_levels (level, name, min_balance, max_daily_tasks, commission_rate, benefits, created_at, updated_at) VALUES
        (1, 'Bronze', 0.00, 5, 5.00, 'Basic benefits', NOW(), NOW()),
        (2, 'Silver', 1000.00, 10, 7.50, 'Enhanced benefits', NOW(), NOW()),
        (3, 'Gold', 5000.00, 15, 10.00, 'Premium benefits', NOW(), NOW()),
        (4, 'Platinum', 10000.00, 20, 12.50, 'Elite benefits', NOW(), NOW()),
        (5, 'Diamond', 25000.00, 30, 15.00, 'Ultimate benefits', NOW(), NOW())";
        
        $result = executeQuery($vip_sql);
        
        if ($result) {
            echo "<p><strong>VIP levels created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create VIP levels.</strong></p>";
        }
    } else {
        echo "<p>VIP levels already exist ($vip_count levels found).</p>";
    }
    
    // Check products
    $products = fetchAll("SELECT COUNT(*) as count FROM products");
    $product_count = $products[0]['count'] ?? 0;
    
    if ($product_count == 0) {
        echo "<p>No products found. Creating sample products...</p>";
        
        $product_sql = "INSERT INTO products (name, description, price, commission_rate, min_vip_level, max_vip_level, category_id, status, created_at, updated_at) VALUES
        ('Product A', 'Sample product for testing', 50.00, 10.00, 1, 5, 1, 'active', NOW(), NOW()),
        ('Product B', 'Another sample product', 100.00, 15.00, 2, 5, 1, 'active', NOW(), NOW()),
        ('Product C', 'Premium sample product', 200.00, 20.00, 3, 5, 1, 'active', NOW(), NOW())";
        
        $result = executeQuery($product_sql);
        
        if ($result) {
            echo "<p><strong>Sample products created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create sample products.</strong></p>";
        }
    } else {
        echo "<p>Products already exist ($product_count products found).</p>";
    }
    
    // Check product categories
    $categories = fetchAll("SELECT COUNT(*) as count FROM product_categories");
    $category_count = $categories[0]['count'] ?? 0;
    
    if ($category_count == 0) {
        echo "<p>No product categories found. Creating default category...</p>";
        
        $category_sql = "INSERT INTO product_categories (name, description, status, created_at, updated_at) VALUES
        ('General', 'General product category', 'active', NOW(), NOW())";
        
        $result = executeQuery($category_sql);
        
        if ($result) {
            echo "<p><strong>Product category created successfully!</strong></p>";
        } else {
            echo "<p><strong>Failed to create product category.</strong></p>";
        }
    } else {
        echo "<p>Product categories already exist ($category_count categories found).</p>";
    }
    
    echo "<hr>";
    echo "<h2>Setup Complete!</h2>";
    echo "<p>You can now test the dashboard with these credentials:</p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> alice, <strong>Password:</strong> password</li>";
    echo "<li><strong>Username:</strong> bob, <strong>Password:</strong> password</li>";
    echo "<li><strong>Username:</strong> charlie, <strong>Password:</strong> password</li>";
    echo "</ul>";
    echo "<p><a href='login/login.php'>Go to Login Page</a></p>";
    echo "<p><a href='test-db.php'>View Database Status</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error: " . $e->getMessage() . "</h2>";
}
?>
