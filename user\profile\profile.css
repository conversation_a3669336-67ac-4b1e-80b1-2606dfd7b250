/**
 * Bamboo User Application - Profile Page Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* Profile Header */
.profile-header {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.profile-avatar-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.profile-info {
    flex: 1;
}

.profile-username {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.profile-level {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.vip-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

.level-name {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.profile-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

.profile-balance-section {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.balance-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    min-width: 180px;
    backdrop-filter: blur(10px);
}

.balance-card h3 {
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

/* Profile Menu Grid */
.profile-menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.profile-menu-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.menu-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.profile-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    text-decoration: none;
    color: #374151;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.profile-menu-item:hover {
    background: #f8fafc;
    border-color: #3b82f6;
    transform: translateY(-2px);
    text-decoration: none;
    color: #1e3a8a;
}

.menu-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.deposit-icon { background: linear-gradient(135deg, #10b981, #059669); color: white; }
.salary-icon { background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; }
.withdraw-icon { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
.records-icon { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
.edit-icon { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; }
.withdrawal-info-icon { background: linear-gradient(135deg, #06b6d4, #0891b2); color: white; }
.contact-icon { background: linear-gradient(135deg, #84cc16, #65a30d); color: white; }
.notifications-icon { background: linear-gradient(135deg, #f97316, #ea580c); color: white; }

.menu-text {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* Recent Transactions */
.recent-transactions-card {
    margin-bottom: 2rem;
}

.transaction-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.transaction-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    border-left: 4px solid #e5e7eb;
}

.transaction-row:nth-child(odd) {
    background: white;
}

.transaction-type {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.type-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: #6b7280;
}

.type-info {
    display: flex;
    flex-direction: column;
}

.type-name {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.type-date {
    font-size: 0.75rem;
    color: #6b7280;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
}

.transaction-amount.positive {
    color: #10b981;
}

.transaction-amount.negative {
    color: #ef4444;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed { background: #d1fae5; color: #065f46; }
.status-pending { background: #fef3c7; color: #92400e; }
.status-failed { background: #fee2e2; color: #991b1b; }

/* Logout Section */
.profile-logout-section {
    text-align: center;
    margin-bottom: 2rem;
}

.logout-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    text-decoration: none;
    color: white;
}

/* No Data States */
.no-transactions {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.no-data-icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-avatar-section {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-stats {
        justify-content: center;
    }
    
    .profile-balance-section {
        justify-content: center;
    }
    
    .profile-menu-grid {
        grid-template-columns: 1fr;
    }
    
    .menu-items {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .transaction-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
