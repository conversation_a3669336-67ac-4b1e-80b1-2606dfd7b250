/**
 * Bamboo User Dashboard - Dynamic Theme System
 * Company: Notepadsly
 * Version: 1.0
 * Description: Integrates with admin appearance system for dynamic theming
 */

class UserTheme {
    constructor() {
        this.root = document.documentElement;
        this.defaultSettings = {
            primaryColor: '#ff6900',
            secondaryColor: '#ffffff',
            accentColor: '#007bff',
            gradientStart: '#ff6900',
            gradientEnd: '#ff8533'
        };
        this.init();
    }

    init() {
        // Load theme settings from server
        this.loadThemeSettings();
        
        // Apply theme immediately
        this.applyTheme();
        
        // Set up theme change listeners
        this.setupThemeListeners();
        
        // Initialize theme animations
        this.initAnimations();
    }

    loadThemeSettings() {
        // Get settings from global UserApp object (set by PHP)
        if (window.UserApp && window.UserApp.appearanceSettings) {
            this.settings = { ...this.defaultSettings, ...window.UserApp.appearanceSettings };
        } else {
            // Fallback to default settings
            this.settings = { ...this.defaultSettings };
        }
    }

    applyTheme() {
        // Update CSS custom properties for user dashboard
        this.root.style.setProperty('--user-primary', this.settings.primaryColor);
        this.root.style.setProperty('--user-secondary', this.settings.secondaryColor);
        this.root.style.setProperty('--user-accent', this.settings.accentColor);
        this.root.style.setProperty('--user-gradient-start', this.settings.gradientStart);
        this.root.style.setProperty('--user-gradient-end', this.settings.gradientEnd);
        
        // Always maintain white background for user dashboard
        this.root.style.setProperty('--user-background', '#ffffff');
        this.root.style.setProperty('--user-surface', '#ffffff');
        this.root.style.setProperty('--user-card-bg', '#ffffff');
        
        // Update gradient backgrounds
        this.updateGradients();
        
        // Apply theme to specific elements
        this.applyElementStyles();
    }

    updateGradients() {
        const primaryGradient = `linear-gradient(135deg, ${this.settings.primaryColor}, ${this.settings.gradientEnd})`;
        const accentGradient = `linear-gradient(135deg, ${this.settings.accentColor}, ${this.lightenColor(this.settings.accentColor, 20)})`;
        
        this.root.style.setProperty('--user-gradient-primary', primaryGradient);
        this.root.style.setProperty('--user-gradient-accent', accentGradient);
    }

    applyElementStyles() {
        // Update primary buttons
        const primaryButtons = document.querySelectorAll('.user-btn-primary');
        primaryButtons.forEach(btn => {
            btn.style.background = `linear-gradient(135deg, ${this.settings.primaryColor}, ${this.settings.gradientEnd})`;
        });

        // Update accent elements
        const accentElements = document.querySelectorAll('.user-accent');
        accentElements.forEach(el => {
            el.style.color = this.settings.accentColor;
        });

        // Update primary text elements
        const primaryTextElements = document.querySelectorAll('.user-text-primary-theme');
        primaryTextElements.forEach(el => {
            el.style.color = this.settings.primaryColor;
        });
    }

    setupThemeListeners() {
        // Listen for theme updates from admin panel
        window.addEventListener('themeUpdate', (event) => {
            this.settings = { ...this.settings, ...event.detail };
            this.applyTheme();
        });

        // Listen for storage changes (if admin updates theme in another tab)
        window.addEventListener('storage', (event) => {
            if (event.key === 'bamboo_theme_update') {
                this.loadThemeSettings();
                this.applyTheme();
            }
        });
    }

    initAnimations() {
        // Add smooth transitions when theme changes
        const style = document.createElement('style');
        style.textContent = `
            * {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
            }
        `;
        document.head.appendChild(style);

        // Remove transition after initial load
        setTimeout(() => {
            style.remove();
        }, 1000);
    }

    // Utility function to lighten colors
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    // Utility function to darken colors
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    // Public method to update theme settings
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.applyTheme();
    }

    // Public method to get current settings
    getCurrentSettings() {
        return { ...this.settings };
    }

    // Method to animate color transitions
    animateColorTransition(fromColor, toColor, duration = 500) {
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Interpolate between colors
            const interpolatedColor = this.interpolateColor(fromColor, toColor, progress);
            
            // Apply the interpolated color
            this.root.style.setProperty('--user-primary', interpolatedColor);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Ensure final color is set
                this.root.style.setProperty('--user-primary', toColor);
            }
        };
        
        requestAnimationFrame(animate);
    }

    // Utility function to interpolate between two colors
    interpolateColor(color1, color2, factor) {
        const c1 = parseInt(color1.replace("#", ""), 16);
        const c2 = parseInt(color2.replace("#", ""), 16);
        
        const r1 = (c1 >> 16) & 0xff;
        const g1 = (c1 >> 8) & 0xff;
        const b1 = c1 & 0xff;
        
        const r2 = (c2 >> 16) & 0xff;
        const g2 = (c2 >> 8) & 0xff;
        const b2 = c2 & 0xff;
        
        const r = Math.round(r1 + (r2 - r1) * factor);
        const g = Math.round(g1 + (g2 - g1) * factor);
        const b = Math.round(b1 + (b2 - b1) * factor);
        
        return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
    }
}

// Initialize theme system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize user theme system
    window.UserTheme = new UserTheme();
    
    // Make it globally available
    window.BambooUserTheme = window.UserTheme;
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserTheme;
}
