<?php
/**
 * Bamboo User Application - Device Detection and Routing
 * Company: Notepadsly
 * Version: 1.0
 * Description: Automatic routing between PC and mobile versions
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Device Detection Functions
 */

function isMobileDevice() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Mobile device patterns
    $mobile_patterns = [
        '/Mobile/i',
        '/Android/i',
        '/iPhone/i',
        '/iPad/i',
        '/iPod/i',
        '/BlackBerry/i',
        '/Windows Phone/i',
        '/Opera Mini/i',
        '/IEMobile/i',
        '/Mobile Safari/i',
        '/webOS/i',
        '/Kindle/i',
        '/Silk/i',
        '/Fennec/i',
        '/Maemo/i',
        '/Tablet/i'
    ];
    
    foreach ($mobile_patterns as $pattern) {
        if (preg_match($pattern, $user_agent)) {
            return true;
        }
    }
    
    return false;
}

function isTabletDevice() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Tablet-specific patterns
    $tablet_patterns = [
        '/iPad/i',
        '/Android.*Tablet/i',
        '/Windows.*Touch/i',
        '/Kindle/i',
        '/Silk/i',
        '/PlayBook/i',
        '/Tablet/i'
    ];
    
    foreach ($tablet_patterns as $pattern) {
        if (preg_match($pattern, $user_agent)) {
            return true;
        }
    }
    
    return false;
}

function getDeviceType() {
    if (isTabletDevice()) {
        return 'tablet';
    } elseif (isMobileDevice()) {
        return 'mobile';
    } else {
        return 'desktop';
    }
}

function getScreenSize() {
    // Try to detect screen size from user agent
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Small screen indicators
    if (preg_match('/Mobile.*320x|Mobile.*480x|iPhone.*320x|Android.*320x/i', $user_agent)) {
        return 'small';
    }
    
    // Medium screen indicators
    if (preg_match('/Mobile.*768x|iPad.*768x|Android.*768x/i', $user_agent)) {
        return 'medium';
    }
    
    // Default to large for desktop
    return 'large';
}

/**
 * User Preference Handling
 */

function getUserDevicePreference() {
    // Check if user has manually selected a version
    if (isset($_GET['force_version'])) {
        $version = $_GET['force_version'];
        if (in_array($version, ['mobile', 'desktop'])) {
            // Store preference in session
            $_SESSION['device_preference'] = $version;
            return $version;
        }
    }
    
    // Check session preference
    if (isset($_SESSION['device_preference'])) {
        return $_SESSION['device_preference'];
    }
    
    // Check cookie preference
    if (isset($_COOKIE['bamboo_device_preference'])) {
        $preference = $_COOKIE['bamboo_device_preference'];
        if (in_array($preference, ['mobile', 'desktop'])) {
            $_SESSION['device_preference'] = $preference;
            return $preference;
        }
    }
    
    return null;
}

function setUserDevicePreference($preference) {
    if (in_array($preference, ['mobile', 'desktop'])) {
        $_SESSION['device_preference'] = $preference;
        // Set cookie for 30 days
        setcookie('bamboo_device_preference', $preference, time() + (30 * 24 * 60 * 60), '/');
        return true;
    }
    return false;
}

/**
 * Routing Logic
 */

function determineTargetVersion() {
    // First check user preference
    $user_preference = getUserDevicePreference();
    if ($user_preference) {
        return $user_preference;
    }
    
    // Auto-detect based on device
    $device_type = getDeviceType();
    $screen_size = getScreenSize();
    
    // Routing rules
    if ($device_type === 'desktop') {
        return 'desktop';
    } elseif ($device_type === 'tablet') {
        // Tablets can use either version, prefer desktop for larger screens
        return ($screen_size === 'large') ? 'desktop' : 'mobile';
    } else {
        // Mobile devices use mobile version
        return 'mobile';
    }
}

function getTargetUrl($version) {
    $base_url = BASE_URL;
    
    if ($version === 'mobile') {
        // Check if user-mobile directory exists
        if (is_dir('../user-mobile')) {
            return $base_url . 'user-mobile/';
        } else {
            // Fallback to desktop version with mobile parameter
            return $base_url . 'user/dashboard/?mobile=1';
        }
    } else {
        return $base_url . 'user/dashboard/';
    }
}

/**
 * Main Routing Logic
 */

// Check if user is logged in
if (!isLoggedIn()) {
    // Redirect to login page
    redirect('user/login/');
    exit;
}

// Log device information for analytics
$device_info = [
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'device_type' => getDeviceType(),
    'screen_size' => getScreenSize(),
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
    'timestamp' => date('Y-m-d H:i:s')
];

// You can log this to database for analytics
// logDeviceAccess($device_info);

// Determine target version
$target_version = determineTargetVersion();
$target_url = getTargetUrl($target_version);

// Add device info to session for use in the application
$_SESSION['device_info'] = [
    'type' => getDeviceType(),
    'screen_size' => getScreenSize(),
    'version' => $target_version,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
];

// Redirect to appropriate version
redirect($target_url);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting... - <?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            color: #2c3e50;
        }
        .redirect-container {
            text-align: center;
            padding: 2rem;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top-color: #ff6900;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .redirect-text {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        .device-info {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 1rem;
        }
        .manual-links {
            margin-top: 2rem;
        }
        .manual-links a {
            display: inline-block;
            margin: 0 1rem;
            padding: 0.5rem 1rem;
            background-color: #ff6900;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .manual-links a:hover {
            background-color: #e55a00;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="spinner"></div>
        <div class="redirect-text">Redirecting to <?php echo ucfirst($target_version); ?> Version...</div>
        <div class="device-info">
            Device: <?php echo ucfirst(getDeviceType()); ?> | 
            Screen: <?php echo ucfirst(getScreenSize()); ?>
        </div>
        
        <div class="manual-links">
            <p>If you are not redirected automatically:</p>
            <a href="<?php echo BASE_URL; ?>user/dashboard/">Desktop Version</a>
            <?php if (is_dir('../user-mobile')): ?>
                <a href="<?php echo BASE_URL; ?>user-mobile/">Mobile Version</a>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Fallback redirect in case PHP redirect fails
        setTimeout(function() {
            window.location.href = '<?php echo $target_url; ?>';
        }, 3000);
        
        // Immediate redirect
        window.location.href = '<?php echo $target_url; ?>';
    </script>
</body>
</html>
