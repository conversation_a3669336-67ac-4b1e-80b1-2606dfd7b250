<?php
/**
 * Bamboo User Dashboard - Balance API
 * Company: Notepadsly
 * Version: 1.0
 * Description: API endpoint for user balance information
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON content type
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    $user_id = getCurrentUserId();
    
    // Get user balance information
    $balance_data = getUserBalance($user_id);
    
    if (!$balance_data) {
        throw new Exception('Failed to retrieve balance data');
    }
    
    // Format the response
    $response = [
        'success' => true,
        'data' => [
            'balance' => floatval($balance_data['balance'] ?? 0),
            'commission_balance' => floatval($balance_data['commission_balance'] ?? 0),
            'frozen_balance' => floatval($balance_data['frozen_balance'] ?? 0),
            'total_deposited' => floatval($balance_data['total_deposited'] ?? 0),
            'total_withdrawn' => floatval($balance_data['total_withdrawn'] ?? 0),
            'total_commission_earned' => floatval($balance_data['total_commission_earned'] ?? 0),
            'balance_formatted' => formatCurrency($balance_data['balance'] ?? 0),
            'commission_balance_formatted' => formatCurrency($balance_data['commission_balance'] ?? 0),
            'frozen_balance_formatted' => formatCurrency($balance_data['frozen_balance'] ?? 0),
            'total_deposited_formatted' => formatCurrency($balance_data['total_deposited'] ?? 0),
            'total_withdrawn_formatted' => formatCurrency($balance_data['total_withdrawn'] ?? 0),
            'total_commission_earned_formatted' => formatCurrency($balance_data['total_commission_earned'] ?? 0)
        ],
        'timestamp' => time()
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log error
    logError('Balance API error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to retrieve balance information'
    ]);
}
?>
