<?php
/**
 * Bulk Add 20 Diverse Products for All VIP Levels
 * Admin Tool for Product Database Population
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Page configuration
$page_title = 'Bulk Add Products';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

// Include admin header
include '../includes/admin_header.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_products'])) {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        
        // Define 20 diverse products across all VIP levels and categories
        $products = [
            // VIP 1 Products (Entry Level - $50-$300)
            ['name' => 'Wireless Bluetooth Earbuds', 'price' => 89.99, 'category_id' => 1, 'min_vip_level' => 1, 'commission_rate' => 12.00],
            ['name' => 'Cotton T-Shirt Set', 'price' => 45.00, 'category_id' => 2, 'min_vip_level' => 1, 'commission_rate' => 15.00],
            ['name' => 'LED Desk Lamp', 'price' => 65.00, 'category_id' => 3, 'min_vip_level' => 1, 'commission_rate' => 18.00],
            ['name' => 'Yoga Mat Premium', 'price' => 55.00, 'category_id' => 4, 'min_vip_level' => 1, 'commission_rate' => 20.00],
            ['name' => 'Skincare Starter Kit', 'price' => 79.99, 'category_id' => 5, 'min_vip_level' => 1, 'commission_rate' => 16.00],

            // VIP 2 Products (Intermediate - $300-$800)
            ['name' => 'Smart Watch Series 8', 'price' => 399.00, 'category_id' => 1, 'min_vip_level' => 2, 'commission_rate' => 10.00],
            ['name' => 'Designer Leather Jacket', 'price' => 599.00, 'category_id' => 2, 'min_vip_level' => 2, 'commission_rate' => 8.00],
            ['name' => 'Robot Vacuum Cleaner', 'price' => 449.00, 'category_id' => 3, 'min_vip_level' => 2, 'commission_rate' => 12.00],
            ['name' => 'Professional Dumbbells Set', 'price' => 299.00, 'category_id' => 4, 'min_vip_level' => 2, 'commission_rate' => 15.00],
            ['name' => 'Professional Hair Dryer', 'price' => 349.00, 'category_id' => 5, 'min_vip_level' => 2, 'commission_rate' => 13.00],

            // VIP 3 Products (Advanced - $800-$2000)
            ['name' => 'Gaming Laptop RTX 4060', 'price' => 1299.00, 'category_id' => 1, 'min_vip_level' => 3, 'commission_rate' => 7.00],
            ['name' => 'Luxury Designer Handbag', 'price' => 1599.00, 'category_id' => 2, 'min_vip_level' => 3, 'commission_rate' => 6.00],
            ['name' => 'Smart Home Security System', 'price' => 899.00, 'category_id' => 3, 'min_vip_level' => 3, 'commission_rate' => 9.00],
            ['name' => 'Professional Treadmill', 'price' => 1799.00, 'category_id' => 4, 'min_vip_level' => 3, 'commission_rate' => 8.00],
            ['name' => 'Premium Skincare Collection', 'price' => 999.00, 'category_id' => 5, 'min_vip_level' => 3, 'commission_rate' => 10.00],

            // VIP 4 Products (Premium - $2000-$5000)
            ['name' => 'MacBook Pro M3 Max', 'price' => 3499.00, 'category_id' => 1, 'min_vip_level' => 4, 'commission_rate' => 5.00],
            ['name' => 'Swiss Luxury Watch', 'price' => 4299.00, 'category_id' => 2, 'min_vip_level' => 4, 'commission_rate' => 4.00],
            ['name' => 'Premium Kitchen Appliance Set', 'price' => 2899.00, 'category_id' => 3, 'min_vip_level' => 4, 'commission_rate' => 6.00],
            ['name' => 'Commercial Gym Equipment', 'price' => 3999.00, 'category_id' => 4, 'min_vip_level' => 4, 'commission_rate' => 5.50],

            // VIP 5 Products (Elite - $5000+)
            ['name' => 'Professional Workstation PC', 'price' => 7999.00, 'category_id' => 1, 'min_vip_level' => 5, 'commission_rate' => 3.50],
            ['name' => 'Professional Spa Equipment', 'price' => 15999.00, 'category_id' => 5, 'min_vip_level' => 5, 'commission_rate' => 2.50]
        ];
        
        $added_count = 0;
        $skipped_count = 0;
        $errors = [];
        
        foreach ($products as $product) {
            try {
                // Check if product already exists
                $check_sql = "SELECT id FROM products WHERE name = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("s", $product['name']);
                $check_stmt->execute();
                $exists = $check_stmt->get_result()->num_rows > 0;
                
                if (!$exists) {
                    $product_data = [
                        'name' => $product['name'],
                        'price' => $product['price'],
                        'commission_rate' => $product['commission_rate'],
                        'category_id' => $product['category_id'],
                        'min_vip_level' => $product['min_vip_level'],
                        'max_daily_assignments' => 100,
                        'weight' => 1,
                        'stock' => 100,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if (insertRecord('products', $product_data)) {
                        $added_count++;
                        echo "<div class='alert alert-success'>✅ Added: {$product['name']} (VIP {$product['min_vip_level']}+) - $" . number_format($product['price'], 2) . "</div>";
                    } else {
                        $errors[] = "Failed to add: {$product['name']}";
                    }
                } else {
                    $skipped_count++;
                    echo "<div class='alert alert-warning'>⏭️ Skipped (already exists): {$product['name']}</div>";
                }
            } catch (Exception $e) {
                $errors[] = "Error adding {$product['name']}: " . $e->getMessage();
            }
        }
        
        // Show summary
        echo "<div class='alert alert-info'>";
        echo "<h4>Summary:</h4>";
        echo "<p>Products added: $added_count</p>";
        echo "<p>Products skipped: $skipped_count</p>";
        echo "<p>Total processed: " . count($products) . "</p>";
        echo "</div>";
        
        if (!empty($errors)) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>Errors:</h4>";
            foreach ($errors as $error) {
                echo "<p>❌ $error</p>";
            }
            echo "</div>";
        }
        
        if ($added_count > 0) {
            echo "<div class='alert alert-success'><strong>🎉 Product database successfully populated with $added_count new products!</strong></div>";
        }
    }
}
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Bulk Add Products</h1>
                    <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to Products</a>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Add 20 Diverse Products for All VIP Levels</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>This tool will add 20 carefully selected products:</h6>
                            <ul>
                                <li><strong>VIP 1:</strong> 5 products ($45-$89) - Entry level items</li>
                                <li><strong>VIP 2:</strong> 5 products ($299-$599) - Intermediate items</li>
                                <li><strong>VIP 3:</strong> 5 products ($899-$1799) - Advanced items</li>
                                <li><strong>VIP 4:</strong> 4 products ($2899-$4299) - Premium items</li>
                                <li><strong>VIP 5:</strong> 2 products ($7999-$15999) - Elite items</li>
                            </ul>
                            <p><strong>Categories covered:</strong> Electronics, Fashion, Home & Garden, Sports, Beauty</p>
                            <p><strong>Commission rates:</strong> 2.5% - 20% based on product category and VIP level</p>
                        </div>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" name="add_products" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>Add All 20 Products
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js'
];
include '../includes/admin_footer_scripts.php';
?>
