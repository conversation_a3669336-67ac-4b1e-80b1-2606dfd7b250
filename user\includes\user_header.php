<?php
/**
 * Bamboo User Dashboard - Universal Header
 * Company: Notepadsly
 * Version: 1.0
 * Description: Reusable header component for all user pages
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Ensure user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get user information
$current_user = getCurrentUser();
$user_balance = getUserBalance($current_user['id']);
$user_vip = getUserVipLevel($current_user['id']);
$unread_notifications = getUnreadNotificationsCount($current_user['id']);

// Get appearance settings for dynamic theming
$appearance_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
];

// Get current page for navigation highlighting
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title ?? 'Dashboard'); ?> - <?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>images/favicon.ico">
    
    <!-- CSS Files -->
    <link href="<?php echo BASE_URL; ?>user/assets/css/user-master.css" rel="stylesheet">
    <?php if (isset($page_css)): ?>
        <link href="<?php echo BASE_URL; ?>user/<?php echo $current_dir; ?>/<?php echo $page_css; ?>" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Meta tags -->
    <meta name="description" content="<?php echo htmlspecialchars($page_description ?? 'User Dashboard'); ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo BASE_URL; ?>user/assets/css/user-master.css" as="style">
    <link rel="preload" href="<?php echo ASSETS_URL; ?>js/jquery.min.js" as="script">
</head>
<body class="user-dashboard">
    <!-- Loading Overlay -->
    <div id="user-loading-overlay" class="user-loading-overlay" style="display: none;">
        <div class="user-loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Loading...</div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="user-notifications" class="user-notifications"></div>

    <!-- Header -->
    <header class="user-header">
        <div class="user-container-fluid">
            <div class="user-header-content">
                <!-- Logo Section -->
                <div class="user-header-logo">
                    <a href="<?php echo BASE_URL; ?>user/dashboard/" class="logo-link">
                        <?php if (getAppSetting('logo_url')): ?>
                            <img src="<?php echo getAppSetting('logo_url'); ?>" alt="<?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?>" class="logo-img">
                        <?php else: ?>
                            <span class="logo-text"><?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?></span>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Navigation Menu -->
                <nav class="user-nav">
                    <ul class="user-nav-list">
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/dashboard/" class="user-nav-link <?php echo ($current_dir === 'dashboard') ? 'active' : ''; ?>">
                                <i class="icon-dashboard"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/tasks/" class="user-nav-link <?php echo ($current_dir === 'tasks') ? 'active' : ''; ?>">
                                <i class="icon-tasks"></i>
                                <span>Tasks</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/transactions/" class="user-nav-link <?php echo ($current_dir === 'transactions') ? 'active' : ''; ?>">
                                <i class="icon-transactions"></i>
                                <span>Transactions</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/team/" class="user-nav-link <?php echo ($current_dir === 'team') ? 'active' : ''; ?>">
                                <i class="icon-team"></i>
                                <span>Team</span>
                            </a>
                        </li>
                        <li class="user-nav-item">
                            <a href="<?php echo BASE_URL; ?>user/vip/" class="user-nav-link <?php echo ($current_dir === 'vip') ? 'active' : ''; ?>">
                                <i class="icon-vip"></i>
                                <span>VIP</span>
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- User Info Section -->
                <div class="user-header-info">
                    <!-- Balance Display -->
                    <div class="user-balance-display">
                        <div class="balance-item">
                            <span class="balance-label">Balance:</span>
                            <span class="balance-value user-balance"><?php echo formatCurrency($user_balance['balance'] ?? 0); ?></span>
                        </div>
                        <div class="balance-item">
                            <span class="balance-label">Commission:</span>
                            <span class="balance-value user-commission-balance"><?php echo formatCurrency($user_balance['commission_balance'] ?? 0); ?></span>
                        </div>
                        <button class="refresh-balance user-btn user-btn-sm" title="Refresh Balance">
                            <i class="icon-refresh"></i>
                        </button>
                    </div>

                    <!-- Notifications -->
                    <div class="user-notifications-dropdown">
                        <button class="notifications-toggle" data-toggle="dropdown">
                            <i class="icon-bell"></i>
                            <?php if ($unread_notifications > 0): ?>
                                <span class="notification-badge"><?php echo $unread_notifications; ?></span>
                            <?php endif; ?>
                        </button>
                        <div class="notifications-menu user-dropdown-menu">
                            <div class="notifications-header">
                                <h6>Notifications</h6>
                                <a href="<?php echo BASE_URL; ?>user/notifications/" class="view-all">View All</a>
                            </div>
                            <div class="notifications-list">
                                <!-- Notifications will be loaded via AJAX -->
                                <div class="notification-loading">Loading...</div>
                            </div>
                        </div>
                    </div>

                    <!-- User Dropdown -->
                    <div class="user-dropdown">
                        <button class="user-dropdown-toggle" data-toggle="dropdown">
                            <div class="user-avatar">
                                <?php if ($current_user['avatar']): ?>
                                    <img src="<?php echo $current_user['avatar']; ?>" alt="Avatar" class="avatar-img">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <?php echo strtoupper(substr($current_user['username'], 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="user-info">
                                <div class="username"><?php echo htmlspecialchars($current_user['username']); ?></div>
                                <div class="vip-level">
                                    VIP <?php echo $user_vip['level'] ?? 1; ?> - <?php echo htmlspecialchars($user_vip['name'] ?? 'Bronze'); ?>
                                </div>
                            </div>
                            <i class="icon-chevron-down"></i>
                        </button>
                        
                        <div class="user-dropdown-menu">
                            <a href="<?php echo BASE_URL; ?>user/profile/" class="dropdown-item">
                                <i class="icon-user"></i>
                                <span>Profile</span>
                            </a>
                            <a href="<?php echo BASE_URL; ?>user/deposit/" class="dropdown-item">
                                <i class="icon-deposit"></i>
                                <span>Deposit</span>
                            </a>
                            <a href="<?php echo BASE_URL; ?>user/withdraw/" class="dropdown-item">
                                <i class="icon-withdraw"></i>
                                <span>Withdraw</span>
                            </a>
                            <a href="<?php echo BASE_URL; ?>user/certificate/" class="dropdown-item">
                                <i class="icon-certificate"></i>
                                <span>Certificate</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo BASE_URL; ?>user/logout/" class="dropdown-item user-logout">
                                <i class="icon-logout"></i>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Wrapper -->
    <main class="user-main-content">
        
        <!-- Global JavaScript Variables -->
        <script>
            // Initialize UserApp configuration
            window.UserApp = window.UserApp || {};
            
            // Set configuration from PHP
            Object.assign(window.UserApp, {
                config: {
                    baseUrl: '<?php echo BASE_URL; ?>',
                    assetsUrl: '<?php echo ASSETS_URL; ?>',
                    csrfToken: '<?php echo generateCSRFToken(); ?>',
                    userId: <?php echo $current_user['id']; ?>,
                    username: '<?php echo htmlspecialchars($current_user['username']); ?>',
                    vipLevel: <?php echo $user_vip['level'] ?? 1; ?>,
                    balance: <?php echo $user_balance['balance'] ?? 0; ?>,
                    commissionBalance: <?php echo $user_balance['commission_balance'] ?? 0; ?>
                },
                appearanceSettings: <?php echo json_encode($appearance_settings); ?>
            });
        </script>
