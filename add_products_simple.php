<?php
/**
 * Simple Terminal Script to Add 20 Products
 * Run from Bamboo root: php add_products_simple.php
 */

echo "=== BAMBOO PRODUCT DATABASE POPULATION ===\n";
echo "Adding 20 diverse products for all VIP levels...\n\n";

// Database configuration (corrected to matchmaking)
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'matchmaking';

try {
    // Connect to database
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "✅ Database connected successfully\n\n";
    
    // Check current product count
    $count_result = $conn->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $current_count = $count_result->fetch_assoc()['count'];
    
    echo "Current active products: $current_count\n\n";
    
    // Define 20 diverse products
    $products = [
        // VIP 1 Products (Entry Level)
        ['name' => 'Wireless Bluetooth Earbuds', 'description' => 'High-quality wireless earbuds with noise cancellation', 'price' => 89.99, 'category_id' => 1, 'min_vip_level' => 1, 'commission_rate' => 12.00],
        ['name' => 'Cotton T-Shirt Set', 'description' => 'Comfortable cotton t-shirt set for everyday wear', 'price' => 45.00, 'category_id' => 2, 'min_vip_level' => 1, 'commission_rate' => 15.00],
        ['name' => 'LED Desk Lamp', 'description' => 'Adjustable LED desk lamp with multiple brightness levels', 'price' => 65.00, 'category_id' => 3, 'min_vip_level' => 1, 'commission_rate' => 18.00],
        ['name' => 'Yoga Mat Premium', 'description' => 'Non-slip premium yoga mat for all fitness levels', 'price' => 55.00, 'category_id' => 4, 'min_vip_level' => 1, 'commission_rate' => 20.00],
        ['name' => 'Skincare Starter Kit', 'description' => 'Complete skincare routine kit for beginners', 'price' => 79.99, 'category_id' => 5, 'min_vip_level' => 1, 'commission_rate' => 16.00],
        
        // VIP 2 Products (Intermediate)
        ['name' => 'Smart Watch Series 8', 'description' => 'Advanced smartwatch with health monitoring and GPS', 'price' => 399.00, 'category_id' => 1, 'min_vip_level' => 2, 'commission_rate' => 10.00],
        ['name' => 'Designer Leather Jacket', 'description' => 'Premium leather jacket with modern design', 'price' => 599.00, 'category_id' => 2, 'min_vip_level' => 2, 'commission_rate' => 8.00],
        ['name' => 'Robot Vacuum Cleaner', 'description' => 'Smart robot vacuum with mapping technology', 'price' => 449.00, 'category_id' => 3, 'min_vip_level' => 2, 'commission_rate' => 12.00],
        ['name' => 'Professional Dumbbells Set', 'description' => 'Adjustable dumbbells set for home gym', 'price' => 299.00, 'category_id' => 4, 'min_vip_level' => 2, 'commission_rate' => 15.00],
        ['name' => 'Professional Hair Dryer', 'description' => 'Salon-grade hair dryer with ionic technology', 'price' => 349.00, 'category_id' => 5, 'min_vip_level' => 2, 'commission_rate' => 13.00],
        
        // VIP 3 Products (Advanced)
        ['name' => 'Gaming Laptop RTX 4060', 'description' => 'High-performance gaming laptop with RTX 4060 graphics', 'price' => 1299.00, 'category_id' => 1, 'min_vip_level' => 3, 'commission_rate' => 7.00],
        ['name' => 'Luxury Designer Handbag', 'description' => 'Authentic designer handbag from premium brand', 'price' => 1599.00, 'category_id' => 2, 'min_vip_level' => 3, 'commission_rate' => 6.00],
        ['name' => 'Smart Home Security System', 'description' => 'Complete smart security system with cameras and sensors', 'price' => 899.00, 'category_id' => 3, 'min_vip_level' => 3, 'commission_rate' => 9.00],
        ['name' => 'Professional Treadmill', 'description' => 'Commercial-grade treadmill for serious fitness', 'price' => 1799.00, 'category_id' => 4, 'min_vip_level' => 3, 'commission_rate' => 8.00],
        ['name' => 'Premium Skincare Collection', 'description' => 'Luxury skincare collection with anti-aging formula', 'price' => 999.00, 'category_id' => 5, 'min_vip_level' => 3, 'commission_rate' => 10.00],

        // VIP 4 Products (Premium)
        ['name' => 'MacBook Pro M3 Max', 'description' => 'Latest MacBook Pro with M3 Max chip for professionals', 'price' => 3499.00, 'category_id' => 1, 'min_vip_level' => 4, 'commission_rate' => 5.00],
        ['name' => 'Swiss Luxury Watch', 'description' => 'Handcrafted Swiss luxury watch with automatic movement', 'price' => 4299.00, 'category_id' => 2, 'min_vip_level' => 4, 'commission_rate' => 4.00],
        ['name' => 'Premium Kitchen Appliance Set', 'description' => 'Professional kitchen appliance set for culinary experts', 'price' => 2899.00, 'category_id' => 3, 'min_vip_level' => 4, 'commission_rate' => 6.00],
        ['name' => 'Commercial Gym Equipment', 'description' => 'Professional gym equipment for commercial use', 'price' => 3999.00, 'category_id' => 4, 'min_vip_level' => 4, 'commission_rate' => 5.50],

        // VIP 5 Products (Elite)
        ['name' => 'Professional Workstation PC', 'description' => 'High-end workstation PC for professional computing', 'price' => 7999.00, 'category_id' => 1, 'min_vip_level' => 5, 'commission_rate' => 3.50],
        ['name' => 'Professional Spa Equipment', 'description' => 'Complete spa equipment set for wellness centers', 'price' => 15999.00, 'category_id' => 5, 'min_vip_level' => 5, 'commission_rate' => 2.50]
    ];
    
    $added_count = 0;
    $skipped_count = 0;
    
    echo "Processing " . count($products) . " products...\n";
    echo str_repeat("-", 70) . "\n";
    
    foreach ($products as $index => $product) {
        // Check if product exists
        $check_stmt = $conn->prepare("SELECT id FROM products WHERE name = ?");
        $check_stmt->bind_param("s", $product['name']);
        $check_stmt->execute();
        $exists = $check_stmt->get_result()->num_rows > 0;
        
        if (!$exists) {
            // Insert product
            $insert_stmt = $conn->prepare("INSERT INTO products (name, description, price, commission_rate, category_id, min_vip_level, max_daily_assignments, weight, stock, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, 100, 1, 100, 'active', NOW(), NOW())");
            $insert_stmt->bind_param("ssddii",
                $product['name'],
                $product['description'],
                $product['price'],
                $product['commission_rate'],
                $product['category_id'],
                $product['min_vip_level']
            );
            
            if ($insert_stmt->execute()) {
                $added_count++;
                printf("✅ [%2d/21] Added: %-35s VIP %d+ | $%8.2f | %2.1f%%\n", 
                    $index + 1, 
                    $product['name'], 
                    $product['min_vip_level'], 
                    $product['price'], 
                    $product['commission_rate']
                );
            } else {
                echo "❌ Failed to add: {$product['name']}\n";
            }
        } else {
            $skipped_count++;
            printf("⏭️  [%2d/21] Skipped: %-35s (already exists)\n", 
                $index + 1, 
                $product['name']
            );
        }
    }
    
    // Final count
    $final_count_result = $conn->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $final_count = $final_count_result->fetch_assoc()['count'];
    
    echo str_repeat("-", 70) . "\n";
    echo "=== SUMMARY ===\n";
    echo "✅ Products added: $added_count\n";
    echo "⏭️  Products skipped: $skipped_count\n";
    echo "📊 Total processed: " . count($products) . "\n";
    echo "🗄️  Total active products: $final_count\n";
    
    if ($added_count > 0) {
        echo "\n🎉 SUCCESS: Database populated with $added_count new products!\n";
        echo "🌐 Admin: http://localhost/Bamboo/admin/products/\n";
        echo "👤 User: http://localhost/Bamboo/user/tasks/tasks.php\n";
    } else {
        echo "\n✅ All products already exist in database.\n";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== SCRIPT COMPLETED ===\n";
?>
