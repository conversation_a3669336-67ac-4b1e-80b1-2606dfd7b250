/**
 * Bamboo User Dashboard - About Page Styles
 * Company: Notepadsly
 * Version: 1.0
 * Description: About page styling
 */

/* ===== PAGE HEADER ===== */
.page-header {
    text-align: center;
    margin-bottom: var(--user-spacing-xl);
    padding: var(--user-spacing-xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: var(--user-border-radius-lg);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin: 0 0 var(--user-spacing-sm) 0;
}

.page-subtitle {
    color: var(--user-text-secondary);
    margin: 0;
    font-size: var(--user-font-size-lg);
}

/* ===== COMPANY SECTION ===== */
.company-content {
    display: flex;
    align-items: center;
    gap: var(--user-spacing-xl);
    padding: var(--user-spacing-xl);
}

.company-logo {
    flex-shrink: 0;
}

.logo-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 105, 0, 0.3);
}

.company-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin: 0 0 var(--user-spacing-sm) 0;
}

.company-tagline {
    font-size: var(--user-font-size-lg);
    color: var(--user-text-secondary);
    font-style: italic;
    margin: 0 0 var(--user-spacing-lg) 0;
}

.company-description {
    color: var(--user-text-primary);
    line-height: 1.6;
    margin: 0;
}

/* ===== FEATURES GRID ===== */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--user-spacing-lg);
}

.feature-item {
    text-align: center;
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
}

.feature-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--user-shadow);
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--user-spacing) auto;
    border-radius: var(--user-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.feature-icon.secure { background: linear-gradient(135deg, var(--user-success), #20c997); }
.feature-icon.earning { background: linear-gradient(135deg, var(--user-warning), #ffc107); }
.feature-icon.growth { background: linear-gradient(135deg, var(--user-info), #17a2b8); }
.feature-icon.community { background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end)); }
.feature-icon.support { background: linear-gradient(135deg, #6f42c1, #8e44ad); }
.feature-icon.innovation { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.feature-item h6 {
    margin: 0 0 var(--user-spacing-sm) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.feature-item p {
    margin: 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
    line-height: 1.5;
}

/* ===== MISSION & VISION ===== */
.mission-content,
.vision-content {
    text-align: center;
    padding: var(--user-spacing-lg);
}

.mission-icon,
.vision-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--user-spacing-lg) auto;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.mission-content p,
.vision-content p {
    color: var(--user-text-primary);
    line-height: 1.6;
    margin: 0;
    font-size: var(--user-font-size);
}

/* ===== STATISTICS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--user-spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
    transition: var(--user-transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--user-shadow);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--user-primary);
    margin-bottom: var(--user-spacing-xs);
}

.stat-label {
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== CONTACT SECTION ===== */
.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--user-spacing-xl);
}

.contact-info {
    display: grid;
    gap: var(--user-spacing-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--user-spacing);
    padding: var(--user-spacing);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--user-border-radius);
    background: linear-gradient(135deg, var(--user-primary), var(--user-gradient-end));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h6 {
    margin: 0 0 var(--user-spacing-xs) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.contact-details p {
    margin: 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
}

.contact-cta {
    text-align: center;
    padding: var(--user-spacing-lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--user-border-radius);
}

.contact-cta h6 {
    margin: 0 0 var(--user-spacing-sm) 0;
    color: var(--user-text-primary);
    font-weight: 600;
}

.contact-cta p {
    margin: 0 0 var(--user-spacing-lg) 0;
    color: var(--user-text-secondary);
    font-size: var(--user-font-size-sm);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .company-content {
        flex-direction: column;
        text-align: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .logo-circle {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
}
