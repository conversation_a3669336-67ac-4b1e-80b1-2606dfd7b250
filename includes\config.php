<?php
ob_start(); // Start output buffering at the very beginning of the application

/**
 * Bamboo Web Application - Configuration File
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant unconditionally, as it's a global flag
define('BAMBOO_APP', true);

// Basic error reporting setup
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// Timezone
date_default_timezone_set('UTC');

// Environment detection
$is_localhost = (php_sapi_name() === 'cli') || in_array($_SERVER['HTTP_HOST'] ?? '', ['localhost', '127.0.0.1', '::1']);
$is_bamboo_folder = strpos($_SERVER['REQUEST_URI'], '/bamboo/') !== false;

// Base URL configuration
if ($is_localhost) {
    define('BASE_URL', 'http://localhost/Bamboo/');
    define('ASSETS_URL', 'http://localhost/Bamboo/assets/');
} else {
    // Production - adjust these for your hosting
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    define('BASE_URL', $protocol . $_SERVER['HTTP_HOST'] . '/');
    define('ASSETS_URL', $protocol . $_SERVER['HTTP_HOST'] . '/assets/');
}

// Database configuration
if ($is_localhost) {
    // MAMP local configuration
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'matchmaking');
    define('DB_USER', 'root');
    define('DB_PASS', 'root');
    define('DB_PORT', '3306'); // Default MAMP port, change if different
} else {
    // Production database configuration (update for your hosting)
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'test_db');
    define('DB_USER', 'test_user');
    define('DB_PASS', 'test_pass');
    define('DB_PORT', '3306');
}

// Application settings
define('APP_NAME', 'Bamboo'); // Can be changed by admin
define('COMPANY_NAME', 'Notepadsly');
define('APP_VERSION', '1.0');

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_EXPIRE', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', dirname(__DIR__) . '/uploads/');

// Pagination settings
define('ITEMS_PER_PAGE', 20);
define('ADMIN_ITEMS_PER_PAGE', 50);

// Financial settings (can be overridden by admin settings)
if (!defined('DEFAULT_CURRENCY')) define('DEFAULT_CURRENCY', 'USDT');
if (!defined('CURRENCY_SYMBOL')) define('CURRENCY_SYMBOL', '$');
if (!defined('DECIMAL_PLACES')) define('DECIMAL_PLACES', 2);

// Email settings (for notifications)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', 'noreply@' . $_SERVER['HTTP_HOST']);
define('FROM_NAME', APP_NAME);

// API settings
define('API_RATE_LIMIT', 100); // requests per hour per IP
define('API_VERSION', 'v1');

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_LIFETIME', 3600);

// Debug mode (set to false for production)
define('DEBUG_MODE', true);

// Log file paths
define('ERROR_LOG_PATH', dirname(__DIR__) . '/logs/error.log');
define('ACCESS_LOG_PATH', dirname(__DIR__) . '/logs/access.log');

// Create logs directory if it doesn't exist
$logs_dir = dirname(__DIR__) . '/logs';
if (!is_dir($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Create uploads directory if it doesn't exist
if (!is_dir(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
    // Create subdirectories
    mkdir(UPLOAD_PATH . 'products/', 0755, true);
    mkdir(UPLOAD_PATH . 'avatars/', 0755, true);
    mkdir(UPLOAD_PATH . 'logos/', 0755, true);
}

// Include error handler after all constants are defined, ensuring it's available globally
require_once __DIR__ . '/error_handler.php';
?>
