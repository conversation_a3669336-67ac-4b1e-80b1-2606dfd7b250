<?php
/**
 * Bamboo User Dashboard - Downline Team Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: Referral team management and statistics
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Get current user information
$current_user = getCurrentUser();
$user_id = $current_user['id'];

// Get referral statistics
$referral_stats = [
    'total_referrals' => getReferralCount($user_id),
    'active_referrals' => getActiveReferralCount($user_id),
    'total_commission' => getTotalReferralCommission($user_id),
    'this_month_commission' => getMonthlyReferralCommission($user_id)
];

// Get referral list
$referrals = getReferralList($user_id);

// Page configuration
$page_title = 'Downline Team';
$page_description = 'Manage your referral team and track commissions';
$page_css = 'team.css';
$page_js = 'team.js';

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="page-header user-fade-in">
        <h1 class="page-title">Downline Team</h1>
        <p class="page-subtitle">Manage your referral network and track earnings</p>
    </div>

    <!-- Referral Statistics -->
    <div class="user-row user-mb-4">
        <div class="user-col-3">
            <div class="user-card stat-card">
                <div class="user-card-body">
                    <div class="stat-icon team-icon">
                        <i class="icon-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number"><?php echo $referral_stats['total_referrals']; ?></h3>
                        <p class="stat-label">Total Referrals</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="user-col-3">
            <div class="user-card stat-card">
                <div class="user-card-body">
                    <div class="stat-icon active-icon">
                        <i class="icon-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number"><?php echo $referral_stats['active_referrals']; ?></h3>
                        <p class="stat-label">Active Members</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="user-col-3">
            <div class="user-card stat-card">
                <div class="user-card-body">
                    <div class="stat-icon commission-icon">
                        <i class="icon-dollar"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">USDT <?php echo formatCurrency($referral_stats['total_commission']); ?></h3>
                        <p class="stat-label">Total Commission</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="user-col-3">
            <div class="user-card stat-card">
                <div class="user-card-body">
                    <div class="stat-icon monthly-icon">
                        <i class="icon-calendar"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">USDT <?php echo formatCurrency($referral_stats['this_month_commission']); ?></h3>
                        <p class="stat-label">This Month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invitation Section -->
    <div class="user-card invitation-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Invite Friends</h5>
        </div>
        <div class="user-card-body">
            <div class="invitation-content">
                <div class="invitation-code-section">
                    <label class="user-form-label">Your Invitation Code:</label>
                    <div class="code-input-group">
                        <input type="text" class="user-form-control" value="<?php echo htmlspecialchars($current_user['invitation_code']); ?>" readonly>
                        <button class="user-btn user-btn-primary" data-copy="<?php echo htmlspecialchars($current_user['invitation_code']); ?>">
                            <i class="icon-copy"></i> Copy
                        </button>
                    </div>
                </div>
                
                <div class="invitation-link-section">
                    <label class="user-form-label">Invitation Link:</label>
                    <div class="code-input-group">
                        <?php $invite_link = BASE_URL . 'user/register/?ref=' . $current_user['invitation_code']; ?>
                        <input type="text" class="user-form-control" value="<?php echo $invite_link; ?>" readonly>
                        <button class="user-btn user-btn-primary" data-copy="<?php echo $invite_link; ?>">
                            <i class="icon-copy"></i> Copy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Referral List -->
    <div class="user-card referral-list-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Your Team Members</h5>
        </div>
        <div class="user-card-body">
            <?php if (!empty($referrals)): ?>
                <div class="referral-table-container">
                    <table class="referral-table">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Join Date</th>
                                <th>VIP Level</th>
                                <th>Status</th>
                                <th>Commission Earned</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($referrals as $referral): ?>
                                <tr class="referral-row">
                                    <td class="username-cell">
                                        <div class="user-avatar-small">
                                            <?php echo strtoupper(substr($referral['username'], 0, 1)); ?>
                                        </div>
                                        <span><?php echo htmlspecialchars($referral['username']); ?></span>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($referral['created_at'])); ?></td>
                                    <td>
                                        <span class="vip-badge vip-<?php echo $referral['vip_level']; ?>">
                                            VIP <?php echo $referral['vip_level']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $referral['status']; ?>">
                                            <?php echo ucfirst($referral['status']); ?>
                                        </span>
                                    </td>
                                    <td class="commission-cell">
                                        USDT <?php echo formatCurrency($referral['commission_earned'] ?? 0); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="icon-users"></i>
                    </div>
                    <h4>No Team Members Yet</h4>
                    <p>Start inviting friends to build your team and earn commissions!</p>
                    <button class="user-btn user-btn-primary" onclick="copyInviteCode()">
                        <i class="icon-share"></i>
                        Share Invitation Code
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Commission Rules -->
    <div class="user-card rules-card user-fade-in">
        <div class="user-card-header">
            <h5 class="user-card-title">Commission Rules</h5>
        </div>
        <div class="user-card-body">
            <div class="rules-grid">
                <div class="rule-item">
                    <div class="rule-icon">
                        <i class="icon-percent"></i>
                    </div>
                    <div class="rule-content">
                        <h6>Commission Rate</h6>
                        <p>Earn 5% commission from your direct referrals' task earnings</p>
                    </div>
                </div>
                
                <div class="rule-item">
                    <div class="rule-icon">
                        <i class="icon-clock"></i>
                    </div>
                    <div class="rule-content">
                        <h6>Real-time Earnings</h6>
                        <p>Commissions are credited instantly when your referrals complete tasks</p>
                    </div>
                </div>
                
                <div class="rule-item">
                    <div class="rule-icon">
                        <i class="icon-infinity"></i>
                    </div>
                    <div class="rule-content">
                        <h6>Unlimited Referrals</h6>
                        <p>No limit on the number of people you can invite to your team</p>
                    </div>
                </div>
                
                <div class="rule-item">
                    <div class="rule-icon">
                        <i class="icon-shield"></i>
                    </div>
                    <div class="rule-content">
                        <h6>Lifetime Earnings</h6>
                        <p>Continue earning from your team members' activities indefinitely</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyInviteCode() {
    const code = '<?php echo $current_user['invitation_code']; ?>';
    navigator.clipboard.writeText(code).then(() => {
        UserApp.showNotification('Invitation code copied to clipboard!', 'success');
    });
}
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
