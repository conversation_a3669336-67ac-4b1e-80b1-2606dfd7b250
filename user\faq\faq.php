<?php
/**
 * Bamboo User Dashboard - FAQ Page
 * Company: Notepadsly
 * Version: 1.0
 * Description: Frequently Asked Questions
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('user/login/');
}

// Page configuration
$page_title = 'FAQ';
$page_description = 'Frequently Asked Questions about Kompyte';
$page_css = 'faq.css';
$page_js = 'faq.js';

// FAQ data
$faq_categories = [
    'Getting Started' => [
        [
            'question' => 'How do I start earning on Kompyte?',
            'answer' => 'Simply register an account, complete your profile, and start with the task submission system. You can begin earning USDT immediately by completing product optimization tasks.'
        ],
        [
            'question' => 'What is the minimum amount I need to start?',
            'answer' => 'You need a minimum balance of USDT 100 in your wallet to start participating in tasks. This ensures you can cover the temporary deductions during task completion.'
        ],
        [
            'question' => 'How long does it take to see my first earnings?',
            'answer' => 'Your earnings are credited immediately upon task completion. Most users see their first profits within minutes of completing their first task.'
        ]
    ],
    'Tasks & Earnings' => [
        [
            'question' => 'How do tasks work?',
            'answer' => 'Tasks involve product optimization activities. You start matching, get assigned a product, review it, and submit your completion. The product amount is temporarily deducted and returned with profit upon submission.'
        ],
        [
            'question' => 'How much can I earn per task?',
            'answer' => 'Earnings depend on your VIP level and the product value. Commission rates range from 5% to 20% based on your VIP status. Higher VIP levels unlock more valuable products and better commission rates.'
        ],
        [
            'question' => 'What happens if I don\'t submit a task?',
            'answer' => 'If you don\'t submit a task, the deducted amount remains frozen in your account. You can complete the task later or contact support for assistance. We recommend completing tasks promptly.'
        ]
    ],
    'VIP Levels' => [
        [
            'question' => 'What are VIP levels?',
            'answer' => 'VIP levels determine your daily task limits, commission rates, and access to premium products. There are 5 VIP levels, each offering increased benefits and earning potential.'
        ],
        [
            'question' => 'How do I upgrade my VIP level?',
            'answer' => 'VIP levels are upgraded based on your total task completions, referral activity, and platform engagement. Higher levels unlock better commission rates and more daily tasks.'
        ],
        [
            'question' => 'Do VIP benefits expire?',
            'answer' => 'No, VIP benefits are permanent once achieved. Your VIP level can only increase, never decrease, ensuring your earning potential continues to grow.'
        ]
    ],
    'Payments & Withdrawals' => [
        [
            'question' => 'How do I withdraw my earnings?',
            'answer' => 'Navigate to the Withdraw section, enter your USDT wallet address, specify the amount (minimum USDT 100), and submit your request. Withdrawals are processed within 24 hours.'
        ],
        [
            'question' => 'Are there any withdrawal fees?',
            'answer' => 'Standard blockchain network fees apply for USDT transfers. These fees are minimal and clearly displayed before you confirm your withdrawal.'
        ],
        [
            'question' => 'How long do withdrawals take?',
            'answer' => 'Most withdrawals are processed within 2-24 hours. During high volume periods, it may take up to 48 hours. You\'ll receive email confirmation once processed.'
        ]
    ],
    'Referrals & Team' => [
        [
            'question' => 'How does the referral system work?',
            'answer' => 'Share your unique invitation code or link with friends. When they register and complete tasks, you earn 5% commission from their task earnings. There\'s no limit to how many people you can refer.'
        ],
        [
            'question' => 'When do I receive referral commissions?',
            'answer' => 'Referral commissions are credited instantly when your referred users complete tasks. You can track all referral earnings in your Team section.'
        ],
        [
            'question' => 'Can I build multiple levels of referrals?',
            'answer' => 'Currently, the system supports direct referrals (1 level). We may introduce multi-level referrals in future updates based on user feedback and platform growth.'
        ]
    ]
];

// Include header
include '../includes/user_header.php';
?>

<div class="user-container">
    <!-- Page Header -->
    <div class="page-header user-fade-in">
        <h1 class="page-title">Frequently Asked Questions</h1>
        <p class="page-subtitle">Find answers to common questions about Kompyte</p>
    </div>

    <!-- Search Box -->
    <div class="user-card search-card user-fade-in">
        <div class="user-card-body">
            <div class="search-container">
                <div class="search-input-group">
                    <input type="text" id="faqSearch" class="user-form-control" placeholder="Search for answers...">
                    <button class="search-btn" onclick="searchFAQ()">
                        <i class="icon-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Categories -->
    <div class="faq-container">
        <?php foreach ($faq_categories as $category => $questions): ?>
            <div class="faq-category user-fade-in">
                <div class="category-header">
                    <h3 class="category-title"><?php echo htmlspecialchars($category); ?></h3>
                    <span class="question-count"><?php echo count($questions); ?> questions</span>
                </div>
                
                <div class="faq-items">
                    <?php foreach ($questions as $index => $faq): ?>
                        <div class="faq-item" data-category="<?php echo strtolower(str_replace(' ', '-', $category)); ?>">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span class="question-text"><?php echo htmlspecialchars($faq['question']); ?></span>
                                <span class="toggle-icon">
                                    <i class="icon-chevron-down"></i>
                                </span>
                            </div>
                            <div class="faq-answer">
                                <div class="answer-content">
                                    <p><?php echo htmlspecialchars($faq['answer']); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Contact Support -->
    <div class="user-card support-card user-fade-in">
        <div class="user-card-body">
            <div class="support-content">
                <div class="support-info">
                    <h5>Still have questions?</h5>
                    <p>Can't find the answer you're looking for? Our support team is here to help you 24/7.</p>
                </div>
                <div class="support-actions">
                    <button class="user-btn user-btn-primary" onclick="contactSupport()">
                        <i class="icon-headphones"></i>
                        Contact Support
                    </button>
                    <button class="user-btn user-btn-outline" onclick="requestFeature()">
                        <i class="icon-lightbulb"></i>
                        Suggest Feature
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFAQ(element) {
    const faqItem = element.parentElement;
    const answer = faqItem.querySelector('.faq-answer');
    const icon = element.querySelector('.toggle-icon i');
    
    // Close other open FAQs in the same category
    const category = faqItem.closest('.faq-category');
    category.querySelectorAll('.faq-item.active').forEach(item => {
        if (item !== faqItem) {
            item.classList.remove('active');
            item.querySelector('.faq-answer').style.maxHeight = '0';
            item.querySelector('.toggle-icon i').style.transform = 'rotate(0deg)';
        }
    });
    
    // Toggle current FAQ
    faqItem.classList.toggle('active');
    
    if (faqItem.classList.contains('active')) {
        answer.style.maxHeight = answer.scrollHeight + 'px';
        icon.style.transform = 'rotate(180deg)';
    } else {
        answer.style.maxHeight = '0';
        icon.style.transform = 'rotate(0deg)';
    }
}

function searchFAQ() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.question-text').textContent.toLowerCase();
        const answer = item.querySelector('.answer-content p').textContent.toLowerCase();
        
        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            item.style.display = 'block';
            if (searchTerm.length > 0) {
                item.classList.add('search-highlight');
            }
        } else {
            item.style.display = searchTerm.length > 0 ? 'none' : 'block';
            item.classList.remove('search-highlight');
        }
    });
    
    // Show/hide categories based on visible items
    document.querySelectorAll('.faq-category').forEach(category => {
        const visibleItems = category.querySelectorAll('.faq-item[style*="block"], .faq-item:not([style*="none"])');
        category.style.display = visibleItems.length > 0 ? 'block' : 'none';
    });
}

function contactSupport() {
    UserApp.showNotification('Support chat will be available soon', 'info');
}

function requestFeature() {
    UserApp.showNotification('Feature request form will be available soon', 'info');
}

// Clear search when input is empty
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('faqSearch');
    searchInput.addEventListener('input', function() {
        if (this.value === '') {
            searchFAQ(); // Reset display
        }
    });
    
    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchFAQ();
        }
    });
});
</script>

<?php
// Include footer
include '../includes/user_footer.php';
?>
