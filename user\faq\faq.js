/**
 * Bamboo User Dashboard - FAQ Page JavaScript
 * Company: Notepadsly
 * Version: 1.0
 * Description: FAQ page functionality
 */

// FAQ management object
const FAQManager = {
    init: function() {
        this.initializeAnimations();
        this.setupEventListeners();
        console.log('FAQManager initialized');
    },

    initializeAnimations: function() {
        // Stagger animation for FAQ categories
        $('.faq-category').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(30px)'
            }).delay(index * 200).animate({
                'opacity': '1'
            }, 600).css('transform', 'translateY(0)');
        });
    },

    setupEventListeners: function() {
        // Search functionality
        $('#faqSearch').on('input', this.handleSearch.bind(this));
        $('#faqSearch').on('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                FAQManager.handleSearch();
            }
        });

        // FAQ toggle functionality
        $('.faq-question').on('click', this.toggleFAQ.bind(this));
    },

    handleSearch: function() {
        const searchTerm = $('#faqSearch').val().toLowerCase().trim();
        const $faqItems = $('.faq-item');
        const $categories = $('.faq-category');
        
        if (searchTerm === '') {
            // Reset all items
            $faqItems.show().removeClass('search-highlight');
            $categories.show();
            return;
        }

        let hasResults = false;

        $faqItems.each(function() {
            const $item = $(this);
            const question = $item.find('.question-text').text().toLowerCase();
            const answer = $item.find('.answer-content p').text().toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                $item.show().addClass('search-highlight');
                hasResults = true;
            } else {
                $item.hide().removeClass('search-highlight');
            }
        });

        // Show/hide categories based on visible items
        $categories.each(function() {
            const $category = $(this);
            const visibleItems = $category.find('.faq-item:visible').length;
            
            if (visibleItems > 0) {
                $category.show();
            } else {
                $category.hide();
            }
        });

        // Show no results message if needed
        if (!hasResults) {
            this.showNoResults();
        } else {
            this.hideNoResults();
        }
    },

    toggleFAQ: function(e) {
        const $question = $(e.currentTarget);
        const $faqItem = $question.closest('.faq-item');
        const $answer = $faqItem.find('.faq-answer');
        const $icon = $question.find('.toggle-icon i');
        const $category = $faqItem.closest('.faq-category');
        
        // Close other open FAQs in the same category
        $category.find('.faq-item.active').not($faqItem).each(function() {
            const $otherItem = $(this);
            const $otherAnswer = $otherItem.find('.faq-answer');
            const $otherIcon = $otherItem.find('.toggle-icon i');
            
            $otherItem.removeClass('active');
            $otherAnswer.css('max-height', '0');
            $otherIcon.css('transform', 'rotate(0deg)');
        });
        
        // Toggle current FAQ
        if ($faqItem.hasClass('active')) {
            // Close
            $faqItem.removeClass('active');
            $answer.css('max-height', '0');
            $icon.css('transform', 'rotate(0deg)');
        } else {
            // Open
            $faqItem.addClass('active');
            $answer.css('max-height', $answer[0].scrollHeight + 'px');
            $icon.css('transform', 'rotate(180deg)');
            
            // Scroll to question if needed
            setTimeout(() => {
                const questionTop = $question.offset().top;
                const windowTop = $(window).scrollTop();
                const windowHeight = $(window).height();
                
                if (questionTop < windowTop || questionTop > windowTop + windowHeight - 200) {
                    $('html, body').animate({
                        scrollTop: questionTop - 100
                    }, 300);
                }
            }, 300);
        }
    },

    showNoResults: function() {
        if ($('.no-results-message').length === 0) {
            const noResultsHTML = `
                <div class="no-results-message user-fade-in">
                    <div class="user-card">
                        <div class="user-card-body">
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="icon-search"></i>
                                </div>
                                <h4>No Results Found</h4>
                                <p>We couldn't find any FAQs matching your search. Try different keywords or contact support for help.</p>
                                <button class="user-btn user-btn-primary" onclick="contactSupport()">
                                    <i class="icon-headphones"></i>
                                    Contact Support
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.faq-container').append(noResultsHTML);
        }
    },

    hideNoResults: function() {
        $('.no-results-message').remove();
    },

    expandAll: function() {
        $('.faq-item').each(function() {
            const $item = $(this);
            const $answer = $item.find('.faq-answer');
            const $icon = $item.find('.toggle-icon i');
            
            $item.addClass('active');
            $answer.css('max-height', $answer[0].scrollHeight + 'px');
            $icon.css('transform', 'rotate(180deg)');
        });
    },

    collapseAll: function() {
        $('.faq-item').each(function() {
            const $item = $(this);
            const $answer = $item.find('.faq-answer');
            const $icon = $item.find('.toggle-icon i');
            
            $item.removeClass('active');
            $answer.css('max-height', '0');
            $icon.css('transform', 'rotate(0deg)');
        });
    }
};

// Global functions
function searchFAQ() {
    FAQManager.handleSearch();
}

function contactSupport() {
    UserApp.showNotification('Support chat will be available soon', 'info');
}

function requestFeature() {
    UserApp.showNotification('Feature request form will be available soon', 'info');
}

// Initialize when page is ready
function initializePage() {
    FAQManager.init();
}

// Export for global access
window.FAQManager = FAQManager;
