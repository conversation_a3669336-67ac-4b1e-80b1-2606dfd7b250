<?php
/**
 * Bamboo User Dashboard - Universal Footer
 * Company: Notepadsly
 * Version: 1.0
 * Description: Reusable footer component for all user pages
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}
?>

    </main>

    <!-- Footer -->
    <footer class="user-footer">
        <div class="user-container-fluid">
            <div class="user-footer-content">
                <!-- Footer Links -->
                <div class="footer-links">
                    <div class="footer-section">
                        <h6>Quick Links</h6>
                        <ul>
                            <li><a href="<?php echo BASE_URL; ?>user/dashboard/">Dashboard</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/tasks/">Tasks</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/transactions/">Transactions</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/vip/">VIP Levels</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h6>Account</h6>
                        <ul>
                            <li><a href="<?php echo BASE_URL; ?>user/profile/">Profile</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/deposit/">Deposit</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/withdraw/">Withdraw</a></li>
                            <li><a href="<?php echo BASE_URL; ?>user/team/">Team</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h6>Support</h6>
                        <ul>
                            <li><a href="<?php echo BASE_URL; ?>user/certificate/">Certificate</a></li>
                            <li><a href="#" data-toggle="modal" data-target="#contactModal">Contact Us</a></li>
                            <li><a href="#" data-toggle="modal" data-target="#helpModal">Help Center</a></li>
                            <li><a href="#" data-toggle="modal" data-target="#faqModal">FAQ</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h6>Company</h6>
                        <ul>
                            <li><a href="#" data-toggle="modal" data-target="#aboutModal">About Us</a></li>
                            <li><a href="#" data-toggle="modal" data-target="#termsModal">Terms of Service</a></li>
                            <li><a href="#" data-toggle="modal" data-target="#privacyModal">Privacy Policy</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Footer Bottom -->
                <div class="footer-bottom">
                    <div class="footer-copyright">
                        <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars(getAppSetting('company_name', COMPANY_NAME)); ?>. All rights reserved.</p>
                        <p class="version-info">Version <?php echo APP_VERSION; ?></p>
                    </div>
                    
                    <div class="footer-social">
                        <!-- Social links can be added here -->
                        <div class="app-info">
                            <span class="app-name"><?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?></span>
                            <span class="app-tagline">Professional Task Management Platform</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="<?php echo ASSETS_URL; ?>js/jquery.min.js"></script>
    <script src="<?php echo ASSETS_URL; ?>js/bootstrap.bundle.min.js"></script>
    
    <!-- User Dashboard Scripts -->
    <script src="<?php echo BASE_URL; ?>user/assets/js/user-theme.js"></script>
    <script src="<?php echo BASE_URL; ?>user/assets/js/user-app.js"></script>
    
    <!-- Page-specific JavaScript -->
    <?php if (isset($page_js)): ?>
        <script src="<?php echo BASE_URL; ?>user/<?php echo basename(dirname($_SERVER['PHP_SELF'])); ?>/<?php echo $page_js; ?>"></script>
    <?php endif; ?>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript for page-specific initialization -->
    <?php if (isset($inline_js)): ?>
        <script>
            <?php echo $inline_js; ?>
        </script>
    <?php endif; ?>

    <!-- Global initialization script -->
    <script>
        $(document).ready(function() {
            // Initialize page-specific functionality
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            // Load notifications
            UserApp.loadNotifications();
            
            // Set up periodic balance refresh (every 5 minutes)
            setInterval(function() {
                UserApp.refreshBalance();
            }, 5 * 60 * 1000);
        });
    </script>

</body>
</html>

<?php
// Clean up output buffer
if (ob_get_level()) {
    ob_end_flush();
}
?>
