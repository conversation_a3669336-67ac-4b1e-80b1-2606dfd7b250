<?php
/**
 * Bamboo User Dashboard - Universal Footer
 * Company: Notepadsly
 * Version: 1.0
 * Description: Reusable footer component for all user pages
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}
?>

    </main>

    <!-- Footer -->
    <footer class="user-footer">
        <div class="user-footer-content">
            <div class="footer-info">
                <div class="footer-copyright">
                    <p>&copy; 2025 Kompytee</p>
                </div>
                <div class="footer-brand">
                    <span class="app-name">Kompyte</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- User Dashboard Scripts -->
    <script src="<?php echo BASE_URL; ?>user/assets/js/user-theme.js"></script>
    <script src="<?php echo BASE_URL; ?>user/assets/js/user-app.js"></script>
    
    <!-- Page-specific JavaScript -->
    <?php if (isset($page_js)): ?>
        <script src="<?php echo BASE_URL; ?>user/<?php echo basename(dirname($_SERVER['PHP_SELF'])); ?>/<?php echo $page_js; ?>"></script>
    <?php endif; ?>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript for page-specific initialization -->
    <?php if (isset($inline_js)): ?>
        <script>
            <?php echo $inline_js; ?>
        </script>
    <?php endif; ?>

    <!-- Global initialization script -->
    <script>
        $(document).ready(function() {
            // Initialize page-specific functionality
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            // Load notifications
            UserApp.loadNotifications();
            
            // Set up periodic balance refresh (every 5 minutes)
            setInterval(function() {
                UserApp.refreshBalance();
            }, 5 * 60 * 1000);
        });
    </script>

</body>
</html>

<?php
// Clean up output buffer
if (ob_get_level()) {
    ob_end_flush();
}
?>
