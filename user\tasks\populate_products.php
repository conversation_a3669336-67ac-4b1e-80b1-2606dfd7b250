<?php
/**
 * Product Database Population Script
 * Adds diverse products for the task matching system
 */

require_once '../includes/config.php';
require_once '../includes/db.php';

// Check if user is logged in and has admin privileges (optional security check)
session_start();

echo "<h2>Product Database Population</h2>";

try {
    // First, check current product count
    $count_sql = "SELECT COUNT(*) as count FROM products WHERE status = 'active'";
    $count_result = $conn->query($count_sql);
    $current_count = $count_result->fetch_assoc()['count'];
    
    echo "<p>Current active products: $current_count</p>";
    
    // Add diverse products
    $products = [
        [
            'name' => 'iPhone 15 Pro Max',
            'description' => 'Latest Apple smartphone with titanium design and advanced camera system',
            'image_url' => '/uploads/products/iphone15-pro-max.jpg',
            'price' => 1199.00,
            'commission_rate' => 8.50,
            'category_id' => 1,
            'min_vip_level' => 1,
            'stock' => 100
        ],
        [
            'name' => 'Samsung Galaxy S24 Ultra',
            'description' => 'Premium Android smartphone with S Pen and AI features',
            'image_url' => '/uploads/products/galaxy-s24-ultra.jpg',
            'price' => 1099.00,
            'commission_rate' => 8.00,
            'category_id' => 1,
            'min_vip_level' => 1,
            'stock' => 100
        ],
        [
            'name' => 'MacBook Air M3',
            'description' => 'Ultra-thin laptop with M3 chip for everyday computing',
            'image_url' => '/uploads/products/macbook-air-m3.jpg',
            'price' => 1299.00,
            'commission_rate' => 10.00,
            'category_id' => 1,
            'min_vip_level' => 2,
            'stock' => 50
        ],
        [
            'name' => 'Sony WH-1000XM5',
            'description' => 'Industry-leading noise canceling wireless headphones',
            'image_url' => '/uploads/products/sony-headphones.jpg',
            'price' => 399.00,
            'commission_rate' => 15.00,
            'category_id' => 1,
            'min_vip_level' => 1,
            'stock' => 150
        ],
        [
            'name' => 'Nike Air Jordan 1',
            'description' => 'Classic basketball sneakers with premium leather construction',
            'image_url' => '/uploads/products/air-jordan-1.jpg',
            'price' => 170.00,
            'commission_rate' => 12.00,
            'category_id' => 2,
            'min_vip_level' => 1,
            'stock' => 200
        ],
        [
            'name' => 'Rolex Submariner',
            'description' => 'Luxury Swiss diving watch with automatic movement',
            'image_url' => '/uploads/products/rolex-submariner.jpg',
            'price' => 8500.00,
            'commission_rate' => 5.00,
            'category_id' => 2,
            'min_vip_level' => 4,
            'stock' => 10
        ],
        [
            'name' => 'Louis Vuitton Handbag',
            'description' => 'Designer leather handbag with iconic monogram pattern',
            'image_url' => '/uploads/products/lv-handbag.jpg',
            'price' => 2200.00,
            'commission_rate' => 6.00,
            'category_id' => 2,
            'min_vip_level' => 3,
            'stock' => 25
        ],
        [
            'name' => 'Dyson V15 Detect',
            'description' => 'Cordless vacuum cleaner with laser dust detection',
            'image_url' => '/uploads/products/dyson-v15.jpg',
            'price' => 749.00,
            'commission_rate' => 10.00,
            'category_id' => 3,
            'min_vip_level' => 2,
            'stock' => 75
        ],
        [
            'name' => 'KitchenAid Stand Mixer',
            'description' => 'Professional-grade stand mixer for baking enthusiasts',
            'image_url' => '/uploads/products/kitchenaid-mixer.jpg',
            'price' => 449.00,
            'commission_rate' => 12.00,
            'category_id' => 3,
            'min_vip_level' => 1,
            'stock' => 100
        ],
        [
            'name' => 'Peloton Bike+',
            'description' => 'Interactive exercise bike with live and on-demand classes',
            'image_url' => '/uploads/products/peloton-bike.jpg',
            'price' => 2495.00,
            'commission_rate' => 8.00,
            'category_id' => 4,
            'min_vip_level' => 3,
            'stock' => 20
        ],
        [
            'name' => 'Apple Watch Series 9',
            'description' => 'Advanced smartwatch with health monitoring and fitness tracking',
            'image_url' => '/uploads/products/apple-watch-s9.jpg',
            'price' => 429.00,
            'commission_rate' => 11.00,
            'category_id' => 4,
            'min_vip_level' => 1,
            'stock' => 120
        ],
        [
            'name' => 'Dyson Airwrap',
            'description' => 'Multi-styler for hair styling without extreme heat damage',
            'image_url' => '/uploads/products/dyson-airwrap.jpg',
            'price' => 599.00,
            'commission_rate' => 13.00,
            'category_id' => 5,
            'min_vip_level' => 2,
            'stock' => 60
        ]
    ];
    
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($products as $product) {
        // Check if product already exists
        $check_sql = "SELECT id FROM products WHERE name = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $product['name']);
        $check_stmt->execute();
        $exists = $check_stmt->get_result()->num_rows > 0;
        
        if (!$exists) {
            $insert_sql = "INSERT INTO products (name, description, image_url, price, commission_rate, category_id, min_vip_level, stock, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("sssddiiii", 
                $product['name'],
                $product['description'],
                $product['image_url'],
                $product['price'],
                $product['commission_rate'],
                $product['category_id'],
                $product['min_vip_level'],
                $product['stock']
            );
            
            if ($insert_stmt->execute()) {
                echo "<p>✅ Added: {$product['name']}</p>";
                $added_count++;
            } else {
                echo "<p>❌ Failed to add: {$product['name']}</p>";
            }
        } else {
            echo "<p>⏭️ Skipped (already exists): {$product['name']}</p>";
            $skipped_count++;
        }
    }
    
    // Final count
    $final_count_result = $conn->query($count_sql);
    $final_count = $final_count_result->fetch_assoc()['count'];
    
    echo "<hr>";
    echo "<h3>Summary:</h3>";
    echo "<p>Products added: $added_count</p>";
    echo "<p>Products skipped: $skipped_count</p>";
    echo "<p>Total active products: $final_count</p>";
    echo "<p><strong>Database population completed successfully!</strong></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
hr { margin: 20px 0; }
</style>
